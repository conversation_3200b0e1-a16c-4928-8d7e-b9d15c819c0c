/**
 * Update Password Form Component
 */
import { WidgetConfig, AuthUrls } from '../../lib/types';
export interface UpdatePasswordFormOptions {
    config: WidgetConfig;
    authUrls: AuthUrls | null;
    apiBase: string;
    container: HTMLElement;
}
export declare class UpdatePasswordForm {
    private options;
    constructor(options: UpdatePasswordFormOptions);
    /**
     * Renders the update password form
     */
    render(): void;
    /**
     * Handles form submission
     */
    private handleSubmit;
    /**
     * Handles successful password update
     */
    private handleSuccessfulUpdate;
}
