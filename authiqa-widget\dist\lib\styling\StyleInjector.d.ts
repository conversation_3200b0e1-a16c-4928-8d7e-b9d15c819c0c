/**
 * Style injection and theme management utilities
 */
import { WidgetCustomization } from '../customization-types';
import { WidgetConfig } from '../types';
export declare class StyleInjector {
    /**
     * Injects styles based on configuration
     */
    static injectStyles(config: WidgetConfig): void;
    /**
     * Generates default theme styles
     */
    private static generateDefaultStyles;
    /**
     * Updates theme dynamically
     */
    static updateTheme(newTheme: 'light' | 'dark', config: WidgetConfig): void;
    /**
     * Generates custom styles from customization object
     */
    static generateCustomStyles(customization: WidgetCustomization): string;
}
