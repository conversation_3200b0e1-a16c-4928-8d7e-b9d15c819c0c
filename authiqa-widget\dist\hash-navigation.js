!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.Authiqa=e():t.Authiqa=e()}(this,(()=>(()=>{"use strict";return document.addEventListener("DOMContentLoaded",(function(){setTimeout((function(){if("function"==typeof window.AuthiqaWidget){var t,e=document.querySelector("script[data-public-key]"),i=(null==e?void 0:e.getAttribute("action"))||"signin";window.addEventListener("hashchange",o),o()}else console.error("AuthiqaWidget not properly loaded");function u(){var e,i,u,o,n,r,d;return t||(t=new window.AuthiqaWidget({publicKey:(null===(e=document.querySelector("script[data-public-key]"))||void 0===e?void 0:e.getAttribute("data-public-key"))||"",container:"authiqa",mode:"popup",theme:(null===(i=document.querySelector("script[theme]"))||void 0===i?void 0:i.getAttribute("theme"))||"light",organizationDomain:"authiqa.com",verifyAuthPath:null===(u=document.querySelector("script[verifyAuthPath]"))||void 0===u?void 0:u.getAttribute("verifyAuthPath"),resendAuthPath:null===(o=document.querySelector("script[resendAuthPath]"))||void 0===o?void 0:o.getAttribute("resendAuthPath"),successAuthPath:null===(n=document.querySelector("script[successAuthPath]"))||void 0===n?void 0:n.getAttribute("successAuthPath"),signinAuthPath:null===(r=document.querySelector("script[signinAuthPath]"))||void 0===r?void 0:r.getAttribute("signinAuthPath"),resetAuthPath:null===(d=document.querySelector("script[resetAuthPath]"))||void 0===d?void 0:d.getAttribute("resetAuthPath")})),t}function o(){var t=window.location.hash.substring(1);["signin","signup","verify","reset","update","resend"].includes(t)?u().show(t):""===t&&u().show(i)}}),500)})),{}})()));