type EventCallback = (data?: any) => void;
export declare class EventManager {
    private static listeners;
    static subscribe(event: string, callback: EventCallback): void;
    static unsubscribe(event: string, callback: EventCallback): void;
    static emit(event: string, data?: any): void;
}
export declare const STYLE_EVENTS: {
    THEME_CHANGED: string;
    STYLES_DISABLED: string;
    STYLES_ENABLED: string;
};
export {};
