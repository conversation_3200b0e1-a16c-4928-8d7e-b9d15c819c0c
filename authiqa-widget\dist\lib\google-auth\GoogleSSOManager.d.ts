/**
 * Google SSO integration manager
 */
export interface GoogleSSOConfig {
    enabled: boolean;
    clientId: string;
}
export interface GoogleSSOOptions {
    apiBase: string;
    publicKey: string;
    successAuthPath?: string;
    authUrls?: {
        successful?: string;
    };
    onSuccess?: (redirectUrl: string) => void;
    onError?: (message: string) => void;
}
export declare class GoogleSSOManager {
    private static scriptLoaded;
    private static oneTapDismissed;
    /**
     * Loads Google Identity Services script if not already loaded
     */
    private static loadGoogleScript;
    /**
     * Handles Google authentication response
     */
    private static handleGoogleAuth;
    /**
     * Shows Google One Tap prompt (only once per session)
     */
    static showOneTapPrompt(config: GoogleSSOConfig, options: GoogleSSOOptions): Promise<void>;
    /**
     * Creates and renders Google Sign-In button
     */
    static renderGoogleButton(container: HTMLElement, config: GoogleSSOConfig, options: GoogleSSOOptions, buttonText?: 'signin_with' | 'signup_with'): Promise<void>;
    /**
     * Creates Google button container with proper styling
     */
    static createGoogleButtonContainer(id: string): HTMLDivElement;
    /**
     * Forces Google button to take full width by applying styles directly
     */
    private static forceButtonWidth;
    /**
     * Resets One Tap state (useful for testing)
     */
    static resetOneTapState(): void;
}
