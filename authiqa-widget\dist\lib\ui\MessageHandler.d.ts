/**
 * Message display and handling utilities
 */
export declare class MessageHandler {
    /**
     * Shows a message to the user with optional redirect
     */
    static showMessage(displayMessage: string, type: 'success' | 'error' | 'warning', redirect?: string): void;
    /**
     * Gets custom success message based on current action
     */
    static getCustomSuccessMessage(defaultMessage: string, currentAction: string | null, messages?: Record<string, string>): string;
}
