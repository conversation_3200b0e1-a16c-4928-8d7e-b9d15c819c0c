/**
 * Sign Up Form Component
 */

import { WidgetConfig, AuthUrls, SignupResponse } from '../../lib/types';
import { FormElements } from '../../lib/ui/FormElements';
import { MessageHandler } from '../../lib/ui/MessageHandler';
import { LoadingStates } from '../../lib/ui/LoadingStates';
import { PasswordValidator } from '../../lib/validation/PasswordValidator';
import { GoogleSSOManager, GoogleSSOConfig } from '../../lib/google-auth/GoogleSSOManager';

export interface SignUpFormOptions {
    config: WidgetConfig;
    authUrls: AuthUrls | null;
    apiBase: string;
    googleSsoConfig?: GoogleSSOConfig;
    container: HTMLElement;
}

export class SignUpForm {
    private options: SignUpFormOptions;

    constructor(options: SignUpFormOptions) {
        this.options = options;
    }

    /**
     * Renders the sign-up form
     */
    render(): void {
        const { config, authUrls, container } = this.options;
        container.innerHTML = '';

        // Title
        const title = document.createElement('h1');
        title.classList.add('authiqa-title');
        title.textContent = config.customization?.typography?.titleText?.signupText || 'Sign up';
        container.appendChild(title);

        // Form
        const form = document.createElement('form');
        form.classList.add('authiqa-form');
        form.style.cssText = `
            display: flex;
            flex-direction: column;
            gap: 1rem;
        `;

        // Username field
        const { container: usernameContainer, input: username } = FormElements.createLabeledInput(
            'text',
            'username',
            config.customization?.inputs?.usernamePlaceholder || 'Username',
            config.customization?.inputs?.usernameLabel || 'Username'
        );

        // Email field
        const { container: emailContainer, input: email } = FormElements.createLabeledInput(
            'email',
            'email',
            config.customization?.inputs?.emailPlaceholder || 'Email Address',
            config.customization?.inputs?.emailLabel || 'Email'
        );

        // Password field
        const { container: passwordContainer, input: password } = FormElements.createPasswordField(
            config.customization?.inputs?.passwordPlaceholder || 'Password',
            'password',
            config.customization?.inputs?.passwordLabel || 'Password'
        );

        // Terms and Privacy links
        const termsDiv = this.createTermsAndPrivacySection();

        // Submit button
        const submit = document.createElement('button');
        submit.setAttribute('type', 'submit');
        submit.classList.add('authiqa-button');
        submit.textContent = config.customization?.buttons?.signupText || 'Sign Up';
        submit.style.marginTop = '0.5rem';

        // Add elements to form
        form.appendChild(usernameContainer);
        form.appendChild(emailContainer);
        form.appendChild(passwordContainer);
        if (termsDiv) form.appendChild(termsDiv);
        form.appendChild(submit);

        // Form submission handler
        form.addEventListener('submit', async (event) => {
            event.preventDefault();
            await this.handleSubmit({
                username: username.value,
                email: email.value,
                password: password.value
            }, submit);
        });

        container.appendChild(form);

        // Add Google SSO button if enabled
        this.addGoogleSSO(form, submit);

        // Add navigation link
        this.addNavigationLink(form);
    }

    /**
     * Creates terms and privacy section
     */
    private createTermsAndPrivacySection(): HTMLDivElement | null {
        const { config } = this.options;

        if (!config.termsAndConditions && !config.privacy) {
            return null;
        }

        const termsDiv = document.createElement('div');
        termsDiv.className = 'terms-privacy';

        let termsText = 'By signing up, you agree to our ';
        const links: string[] = [];

        if (config.termsAndConditions) {
            links.push(`<a href="${config.termsAndConditions}" target="_blank">Terms and Conditions</a>`);
        }

        if (config.privacy) {
            links.push(`<a href="${config.privacy}" target="_blank">Privacy Policy</a>`);
        }

        termsText += links.join(' and ') + '.';
        termsDiv.innerHTML = termsText;

        return termsDiv;
    }

    /**
     * Handles form submission
     */
    private async handleSubmit(
        formData: {
            username: string;
            email: string;
            password: string;
        },
        submitButton: HTMLButtonElement
    ): Promise<void> {
        const { config, apiBase } = this.options;

        // Validate password strength
        const passwordValidation = PasswordValidator.validatePassword(formData.password);
        if (!passwordValidation.isValid) {
            MessageHandler.showMessage(passwordValidation.error!.message, 'error');
            return;
        }

        LoadingStates.setLoadingState(submitButton, true, 'signup', config.messages);

        const requestData = {
            username: formData.username,
            email: formData.email,
            password: formData.password,
            parentPublicKey: config.publicKey,
        };

        try {
            const response = await fetch(`${apiBase}/auth/signup`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(requestData),
            });

            const result: SignupResponse = await response.json();

            if (response.status === 200 && result.success && 'data' in result) {
                this.handleSuccessfulSignup();
            } else if (!result.success && 'error' in result) {
                MessageHandler.showMessage(result.error.message, 'error');
            } else {
                MessageHandler.showMessage('An unexpected error occurred', 'error');
            }
        } catch (error) {
            console.error('Signup network error:', error);
            MessageHandler.showMessage(
                'Network error: Unable to connect to the server. Please check your connection and try again.',
                'error'
            );
        } finally {
            LoadingStates.setLoadingState(submitButton, false, 'signup', config.messages);
        }
    }

    /**
     * Handles successful signup response
     */
    private handleSuccessfulSignup(): void {
        const { config, authUrls } = this.options;

        const message = MessageHandler.getCustomSuccessMessage(
            'Account created successfully! Please check your email to verify your account.',
            'signup',
            config.messages
        );

        // Redirect to resend confirmation page after successful signup
        const redirectUrl = config.resendAuthPath || authUrls?.resend || '';
        MessageHandler.showMessage(message, 'success', redirectUrl);
    }

    /**
     * Adds Google SSO button if enabled
     */
    private addGoogleSSO(form: HTMLFormElement, submitButton: HTMLButtonElement): void {
        const { googleSsoConfig, apiBase, config, authUrls } = this.options;

        if (!googleSsoConfig?.enabled || !googleSsoConfig.clientId) {
            return;
        }

        const googleContainer = GoogleSSOManager.createGoogleButtonContainer('google-signup-button-container');

        const googleOptions = {
            apiBase,
            publicKey: config.publicKey,
            successAuthPath: config.successAuthPath,
            authUrls: authUrls ? { successful: authUrls.successful } : undefined
        };

        // Render Google button
        GoogleSSOManager.renderGoogleButton(
            googleContainer,
            googleSsoConfig,
            googleOptions,
            'signup_with'
        );

        form.insertBefore(googleContainer, submitButton.nextSibling);
    }

    /**
     * Adds navigation link to signin
     */
    private addNavigationLink(form: HTMLFormElement): void {
        const { config, authUrls } = this.options;
        const navLinks = config.customization?.navLinks;

        const signinPath = authUrls?.signin || '#';
        const signinPrompt = navLinks?.signinPrompt || 'Already have an account?';
        const signinLinkText = navLinks?.signinLinkText || 'Sign In';

        const navDiv = document.createElement('div');
        navDiv.className = 'alternate-action';
        navDiv.innerHTML = `${signinPrompt} <a href="${signinPath}">${signinLinkText}</a>`;

        form.appendChild(navDiv);
    }
}
