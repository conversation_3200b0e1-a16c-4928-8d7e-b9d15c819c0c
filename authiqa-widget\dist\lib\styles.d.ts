import { WidgetConfig } from './types';
export declare const getStyleContent: (theme: 'light' | 'dark') => string;
export declare const getComponentStyles: (theme?: 'light' | 'dark') => {
    modal: {
        overlay: {
            position: string;
            top: number;
            left: number;
            width: string;
            height: string;
            backgroundColor: string;
            zIndex: number;
        };
        container: {
            position: string;
            width: string;
            margin: string;
            backgroundColor: string;
            color: string;
            borderRadius: string;
            padding: string;
            border: string;
        };
    };
    iframe: {
        border: string;
        width: string;
        height: string;
        backgroundColor: string;
    };
    message: {
        position: string;
        top: string;
        left: string;
        transform: string;
        padding: string;
        borderRadius: string;
        fontSize: string;
        fontWeight: string;
        opacity: string;
        transition: string;
    };
    messageSuccess: {
        backgroundColor: string;
        color: string;
    };
    messageError: {
        backgroundColor: string;
        color: string;
    };
    messageShow: {
        opacity: string;
    };
};
export declare const generatePageLayoutStyles: (config: WidgetConfig) => string;
export declare const generateTermsContainerStyles: (config: WidgetConfig) => string;
