import { WidgetCustomization } from './customization-types';
export interface AuthUrls {
    signup: string;
    signin: string;
    verify: string;
    reset: string;
    update: string;
    resend: string;
    successful: string;
}
export interface WidgetConfig {
    publicKey: string;
    container: string;
    mode: 'popup' | 'inline';
    theme?: 'light' | 'dark' | 'none';
    disableStyles?: boolean;
    organizationDomain: string;
    customization?: Partial<WidgetCustomization>;
    termsAndConditions?: string;
    privacy?: string;
    notificationSettings?: string;
    verifyAuthPath?: string;
    updatePasswordPath?: string;
    resendAuthPath?: string;
    successAuthPath?: string;
    signinAuthPath?: string;
    resetAuthPath?: string;
    messages?: {
        signinSuccess?: string;
        signupSuccess?: string;
        resetSuccess?: string;
        updateSuccess?: string;
        resendSuccess?: string;
        verificationSuccess?: string;
        signinLoading?: string;
        signupLoading?: string;
        resetLoading?: string;
        updateLoading?: string;
        resendLoading?: string;
        verificationLoading?: string;
    };
}
export interface ApiError {
    code: string;
    message: string;
}
export interface ApiResponse<T> {
    success: boolean;
    data?: T;
    error?: ApiError;
}
export interface SignupSuccessResponse {
    success: true;
    data: {
        userID: string;
        username: string;
        email: string;
        createdAt: number;
        publicKey: string;
        emailVerified: boolean;
        parentAccount: string | null;
        organizationName: string | null;
        organizationUrl: string | null;
    };
}
export interface SignupErrorResponse {
    success: false;
    error: {
        code: string;
        message: string;
    };
}
export type SignupResponse = SignupSuccessResponse | SignupErrorResponse;
export interface SigninSuccessResponse {
    success: true;
    data: {
        token: {
            accessToken: string;
            refreshToken: string;
            userId: string;
            username: string;
            accountType: 'parent' | 'child';
            parentAccount: string;
            publicKey: string;
            createdAt: number;
        };
        passwordStatus?: {
            expired: boolean;
            daysUntilExpiry: number;
        };
    };
}
export interface SigninErrorResponse {
    success: false;
    error: {
        code: string;
        message: string;
    };
}
export type SigninResponse = SigninSuccessResponse | SigninErrorResponse;
export interface ResendConfirmationSuccessResponse {
    success: true;
    data: {
        message: string;
    };
}
export interface ResendConfirmationErrorResponse {
    success: false;
    error: {
        code: string;
        message: string;
    };
}
export type ResendConfirmationResponse = ResendConfirmationSuccessResponse | ResendConfirmationErrorResponse;
export interface AuthResponse {
    success: boolean;
    data?: {
        status: string;
        lastChecked: number;
    };
    error?: ApiError;
}
export interface OrganizationDetailsResponse {
    message: string;
    authUrls: AuthUrls;
    organizationUrl: string;
    domainRestrictionEnabled: boolean;
    googleSsoConfig?: {
        enabled: boolean;
        clientId: string;
    };
}
