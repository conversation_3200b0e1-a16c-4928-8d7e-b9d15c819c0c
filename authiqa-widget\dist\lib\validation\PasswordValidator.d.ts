/**
 * Password validation utilities
 */
export interface PasswordValidationResult {
    isValid: boolean;
    error?: {
        code: string;
        message: string;
    };
}
export declare class PasswordValidator {
    /**
     * Validates password according to security requirements
     */
    static validatePassword(password: string): PasswordValidationResult;
    /**
     * Checks if two passwords match
     */
    static passwordsMatch(password1: string, password2: string): boolean;
    /**
     * Creates real-time password validation for form fields
     */
    static setupPasswordValidation(passwordInput: HTMLInputElement, confirmPasswordInput?: HTMLInputElement): () => void;
}
