!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.Authiqa=e():t.Authiqa=e()}(this,(()=>(()=>{"use strict";var t={959:(t,e,n)=>{n.d(e,{A:()=>s});var i=n(601),o=n.n(i),a=n(314),r=n.n(a)()(o());r.push([t.id,'/* Base styles */\nbody {\n    margin: 0;\n    min-height: 100vh;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n}\n\nbody[data-theme="dark"] {\n    background-color: #18181b;\n}\n\n.authiqa-container {\n    font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, Oxygen, Ubuntu, Cantarell, \'Open Sans\', \'Helvetica Neue\', sans-serif;\n    max-width: 400px;\n    width: 100%;\n    margin: 2rem;\n    padding: 1.25rem 1.5rem 1.5rem 1.5rem; /* Reduced horizontal padding from 2.5rem to 1.5rem */\n    border-radius: 16px;\n}\n\n.authiqa-container h1 {\n    font-size: 2rem;\n    font-weight: 600;\n    margin-top: 0; /* Added to ensure no extra top margin */\n    margin-bottom: 2rem;\n    color: #1a1a1a;\n    text-align: center;\n}\n\n.authiqa-container form {\n    display: flex;\n    flex-direction: column;\n    gap: 1.5rem;\n}\n\n.authiqa-container label,\n.authiqa-label {\n    font-size: 1rem;\n    font-weight: 400;\n    color: #1a1a1a;\n    margin-bottom: 0.5rem !important; /* Changed to 0.3rem (approximately 5px) */\n    padding-left: 0.09rem !important; /* Added left padding to move labels slightly right */\n    display: block;\n    height: 14px !important; /* Added fixed height */\n    line-height: 14px !important; /* Added line height to match height */\n}\n\n/* Add more spacing between input groups */\n.authiqa-container .authiqa-labeled-input {\n    margin-bottom: 1rem !important; /* Decreased from 1.5rem to 1rem (about 16px) */\n}\n\n.authiqa-container input[type="text"],\n.authiqa-container input[type="email"],\n.authiqa-container input[type="password"] {\n    width: 100%;\n    height: 50px; /* Set height to 64px (14x64) */\n    padding: 0 1rem;\n    font-size: 1rem;\n    border: 1px solid #e5e5e5;\n    border-radius: 4px;\n    background-color: #ffffff;\n    transition: border-color 0.2s ease;\n    box-sizing: border-box;\n}\n\n.authiqa-container input[type="text"]:focus,\n.authiqa-container input[type="email"]:focus,\n.authiqa-container input[type="password"]:focus {\n    outline: none;\n    border-color: #000000;\n}\n\n.authiqa-container input[type="text"]::placeholder,\n.authiqa-container input[type="email"]::placeholder,\n.authiqa-container input[type="password"]::placeholder {\n    color: #a3a3a3;\n}\n\n.authiqa-container .terms-container {\n    display: flex;\n    align-items: flex-start;\n    gap: 0.75rem;\n    margin-bottom: 1rem;\n}\n\n.authiqa-container input[type="checkbox"] {\n    margin-top: 0.25rem; /* Adjusted to align with text */\n    margin-right: 0.5rem; /* Standardized margin */\n    margin-left: 0; /* Reset left margin */\n    position: static; /* Remove relative positioning */\n    top: auto; /* Remove top offset */\n}\n\n.authiqa-container .terms-container label {\n    font-size: 0.875rem;\n    color: #525252;\n    line-height: 1.4;\n    margin-top: 0; /* Ensure no top margin */\n    padding-top: 0; /* Ensure no top padding */\n}\n\n.authiqa-container .terms-container a {\n    color: #000000;\n    text-decoration: none;\n}\n\n.authiqa-container .terms-container a:hover {\n    text-decoration: underline;\n}\n\n.authiqa-container .forgot-password,\n.authiqa-container .alternate-action {\n    color: #ffffff !important;\n    font-size: 0.95rem !important;\n    text-align: left !important;\n}\n.authiqa-container .alternate-action {\n    text-align: center;\n}\n.authiqa-container .forgot-password a,\n.authiqa-container .alternate-action a {\n    color: #10D5C6 !important;\n    text-decoration: underline;\n    font-weight: 500;\n    margin-left: 0.25rem;\n    transition: color 0.2s;\n}\n.authiqa-container .forgot-password a:hover,\n.authiqa-container .alternate-action a:hover {\n    color: #0ea5e9 !important;\n}\n\n/* Update the button styles to include proper centering */\n.authiqa-container button[type="submit"] {\n    width: 100%;\n    height: 40px; /* Changed to fixed 40px height (approximately 14px) */\n    padding: 0 1rem;\n    font-size: 1rem;\n    font-weight: 500;\n    color: #ffffff;\n    background-color: #18181b;\n    border: none;\n    border-radius: 4px;\n    cursor: pointer;\n    transition: all 0.2s ease;\n    position: relative;\n    overflow: hidden;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    line-height: 1;\n    margin-top: 0.5rem; /* Decreased from 1rem to 0.5rem (about 8px) */\n}\n\n.authiqa-container button[type="submit"]:hover {\n    background-color: #27272a;\n    transform: translateY(-1px);\n    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n/* Add this new style for the active/clicked state */\n.authiqa-container button[type="submit"]:active {\n    transform: scale(0.98);\n    box-shadow: none;\n    background-color: #000000;\n}\n\n.authiqa-container button[type="submit"]:disabled {\n    background-color: #71717a;\n    cursor: not-allowed;\n    transform: none;\n    box-shadow: none;\n}\n\n/* Loading state styles */\n.authiqa-container button[type="submit"].loading {\n    position: relative;\n    color: transparent !important;\n}\n\n.authiqa-container button[type="submit"].loading::after {\n    content: \'\';\n    position: absolute;\n    width: 20px;\n    height: 20px;\n    top: 50%;\n    left: 50%;\n    margin: -10px 0 0 -10px;\n    border: 2px solid #ffffff;\n    border-radius: 50%;\n    border-left-color: transparent;\n    animation: button-loading-spinner 1s linear infinite;\n}\n\n/* Loading text container */\n.authiqa-container .loading-text {\n    text-align: center;\n    margin-top: 8px;\n    font-size: 0.875rem;\n    color: #525252;\n}\n\n/* Dark theme loading text */\n.authiqa-container[data-theme="dark"] .loading-text {\n    color: #a1a1aa;\n}\n\n@keyframes button-loading-spinner {\n    0% {\n        transform: rotate(0deg);\n    }\n    100% {\n        transform: rotate(360deg);\n    }\n}\n\n/* Dark theme button styles */\n.authiqa-container[data-theme="dark"] button[type="submit"] {\n    background-color: #ffffff;\n    color: #18181b;\n}\n\n.authiqa-container[data-theme="dark"] button[type="submit"]:hover {\n    background-color: #e5e5e5;\n}\n\n.authiqa-container[data-theme="dark"] button[type="submit"]:disabled {\n    background-color: #a1a1aa;\n    color: #18181b;\n}\n\n.authiqa-container[data-theme="dark"] button[type="submit"].loading::after {\n    border-color: #18181b;\n    border-left-color: transparent;\n}\n\n/* Center alternate-action and set color for both themes */\n.authiqa-container .alternate-action {\n    text-align: center !important;\n    margin-top: 1.5rem;\n    font-size: 0.95rem;\n    color: #1a1a1a !important; /* Force dark text in light theme */\n}\n\n.authiqa-container .alternate-action a {\n    color: #10D5C6 !important;\n    text-decoration: underline;\n    font-weight: 500;\n    margin-left: 0.25rem;\n    transition: color 0.2s;\n}\n\n.authiqa-container .alternate-action a:hover {\n    color: #0ea5e9 !important;\n}\n\n/* Dark theme overrides */\n.authiqa-container[data-theme="dark"] .alternate-action {\n    color: #ffffff !important;\n}\n\n/* Dark theme */\n.authiqa-container[data-theme="dark"] {\n    color: #ffffff;\n    background-color: #27272a;\n}\n\n.authiqa-container[data-theme="dark"] h1 {\n    color: #ffffff;\n}\n\n.authiqa-container[data-theme="dark"] label,\n.authiqa-container[data-theme="dark"] .authiqa-label {\n    color: #ffffff !important; /* Ensure white label text in dark theme */\n    \n}\n\n.authiqa-container[data-theme="dark"] input[type="text"],\n.authiqa-container[data-theme="dark"] input[type="email"],\n.authiqa-container[data-theme="dark"] input[type="password"] {\n    background-color: #18181b;\n    border-color: #3f3f46;\n    color: #ffffff;\n}\n\n.authiqa-container[data-theme="dark"] input[type="text"]:focus,\n.authiqa-container[data-theme="dark"] input[type="email"]:focus,\n.authiqa-container[data-theme="dark"] input[type="password"]:focus {\n    border-color: #ffffff;\n}\n\n.authiqa-container[data-theme="dark"] input[type="text"]::placeholder,\n.authiqa-container[data-theme="dark"] input[type="email"]::placeholder,\n.authiqa-container[data-theme="dark"] input[type="password"]::placeholder {\n    color: #71717a;\n}\n\n.authiqa-container[data-theme="dark"] .terms-container label {\n    color: #a1a1aa;\n}\n\n.authiqa-container[data-theme="dark"] .terms-container a,\n.authiqa-container[data-theme="dark"] .forgot-password a {\n    color: #ffffff;\n}\n\n.authiqa-container[data-theme="dark"] .alternate-action {\n    color: #a1a1aa;\n}\n\n.authiqa-container[data-theme="dark"] .alternate-action a {\n    color: #ffffff;\n}\n\n.authiqa-container .password-field-container {\n    position: relative;\n    width: 100%;\n}\n\n.authiqa-container .password-toggle {\n    position: absolute;\n    right: 12px;\n    top: 50%;\n    transform: translateY(-50%);\n    border: none;\n    background: none;\n    cursor: pointer;\n    padding: 8px;\n    color: #71717a;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    transition: color 0.2s ease;\n}\n\n.authiqa-container .password-toggle:hover {\n    color: #000000;\n}\n\n.authiqa-container[data-theme="dark"] .password-toggle {\n    color: #a1a1aa;\n}\n\n.authiqa-container[data-theme="dark"] .password-toggle:hover {\n    color: #ffffff;\n}\n\n/* Message styles */\n.authiqa-message {\n    position: fixed;\n    top: 20px;\n    left: 50%;\n    transform: translateX(-50%);\n    z-index: 1000;\n    padding: 1rem 2rem;\n    border-radius: 0.375rem;\n    font-size: 0.875rem;\n    line-height: 1.25rem;\n    box-shadow: 0 2px 5px rgba(0,0,0,0.2);\n    animation: slideDown 0.3s ease-out;\n    max-width: 90%;\n    width: auto;\n    opacity: 0;\n    pointer-events: none;\n    transition: opacity 0.3s;\n}\n\n.authiqa-message.show {\n    opacity: 1;\n    pointer-events: auto;\n}\n\n.authiqa-message.success {\n    background-color: #dcfce7;\n    color: #15803d;\n    border-left: 4px solid #22c55e;\n}\n\n.authiqa-message.error {\n    background-color: #fee2e2;\n    color: #b91c1c;\n    border-left: 4px solid #ef4444;\n}\n\n@keyframes slideDown {\n    from {\n        transform: translate(-50%, -100%);\n        opacity: 0;\n    }\n    to {\n        transform: translate(-50%, 0);\n        opacity: 1;\n    }\n}\n\n/* Dark theme message styles */\nbody[data-theme="dark"] .authiqa-message.success {\n    background-color: #064e3b;\n    color: #ffffff;\n    border-left-color: #059669;\n}\n\nbody[data-theme="dark"] .authiqa-message.error {\n    background-color: #7f1d1d;\n    color: #ffffff;\n    border-left-color: #dc2626;\n}\n\n/* Password validation styling */\n.authiqa-container .password-validation-container {\n    display: grid;\n    grid-template-columns: 1fr 1fr; /* Two columns instead of flex-wrap */\n    gap: 0.5rem;\n    margin-top: 0.5rem;\n}\n\n.authiqa-container .validation-item {\n    display: flex;\n    align-items: center;\n    font-size: 0.75rem;\n    color: #a1a1aa;\n}\n\n.authiqa-container .validation-dot {\n    margin-right: 0.25rem;\n    color: #a1a1aa;\n}\n\n.authiqa-container .validation-item.valid .validation-dot,\n.authiqa-container .validation-item.valid .validation-text {\n    color: #10D5C6;\n}\n\n/* Dark theme adjustments */\n.authiqa-container[data-theme="dark"] .validation-item {\n    color: #a1a1aa;\n}\n\n.authiqa-container[data-theme="dark"] .validation-item.valid .validation-dot,\n.authiqa-container[data-theme="dark"] .validation-item.valid .validation-text {\n    color: #10D5C6;\n}\n\n/* Google SSO button styles */\n#google-signin-btn, #google-signup-btn {\n    cursor: pointer;\n    transition: background 0.2s, box-shadow 0.2s, transform 0.1s;\n}\n#google-signin-btn:hover, #google-signup-btn:hover {\n    background: #f7f7f7 !important;\n    box-shadow: 0 2px 8px rgba(60,64,67,.15);\n}\n#google-signin-btn:active, #google-signup-btn:active {\n    transform: scale(0.98);\n    box-shadow: 0 1px 2px rgba(60,64,67,.08);\n}\n\n/* Google button container styles to ensure full width */\n#google-button-container, #google-signup-button-container {\n    width: 100% !important;\n    display: block !important;\n}\n\n/* Override Google\'s default button styling to match our widget width */\n#google-button-container > div, #google-signup-button-container > div {\n    width: 100% !important;\n    display: block !important;\n}\n\n#google-button-container iframe, #google-signup-button-container iframe {\n    width: 100% !important;\n    min-width: 100% !important;\n    max-width: 100% !important;\n    display: block !important;\n}\n\n/* More aggressive targeting for Google button elements */\n#google-button-container * {\n    width: 100% !important;\n    max-width: 100% !important;\n}\n\n#google-signup-button-container * {\n    width: 100% !important;\n    max-width: 100% !important;\n}\n',""]);const s=r},314:t=>{t.exports=function(t){var e=[];return e.toString=function(){return this.map((function(e){var n="",i=void 0!==e[5];return e[4]&&(n+="@supports (".concat(e[4],") {")),e[2]&&(n+="@media ".concat(e[2]," {")),i&&(n+="@layer".concat(e[5].length>0?" ".concat(e[5]):""," {")),n+=t(e),i&&(n+="}"),e[2]&&(n+="}"),e[4]&&(n+="}"),n})).join("")},e.i=function(t,n,i,o,a){"string"==typeof t&&(t=[[null,t,void 0]]);var r={};if(i)for(var s=0;s<this.length;s++){var c=this[s][0];null!=c&&(r[c]=!0)}for(var l=0;l<t.length;l++){var d=[].concat(t[l]);i&&r[d[0]]||(void 0!==a&&(void 0===d[5]||(d[1]="@layer".concat(d[5].length>0?" ".concat(d[5]):""," {").concat(d[1],"}")),d[5]=a),n&&(d[2]?(d[1]="@media ".concat(d[2]," {").concat(d[1],"}"),d[2]=n):d[2]=n),o&&(d[4]?(d[1]="@supports (".concat(d[4],") {").concat(d[1],"}"),d[4]=o):d[4]="".concat(o)),e.push(d))}},e}},601:t=>{t.exports=function(t){return t[1]}},790:(t,e,n)=>{n.r(e),n.d(e,{default:()=>v});var i=n(72),o=n.n(i),a=n(825),r=n.n(a),s=n(659),c=n.n(s),l=n(56),d=n.n(l),u=n(540),h=n.n(u),p=n(494),m=n.n(p),g=n(959),f={};f.styleTagTransform=m(),f.setAttributes=d(),f.insert=c().bind(null,"head"),f.domAPI=r(),f.insertStyleElement=h(),o()(g.A,f);const v=g.A&&g.A.locals?g.A.locals:void 0},72:t=>{var e=[];function n(t){for(var n=-1,i=0;i<e.length;i++)if(e[i].identifier===t){n=i;break}return n}function i(t,i){for(var a={},r=[],s=0;s<t.length;s++){var c=t[s],l=i.base?c[0]+i.base:c[0],d=a[l]||0,u="".concat(l," ").concat(d);a[l]=d+1;var h=n(u),p={css:c[1],media:c[2],sourceMap:c[3],supports:c[4],layer:c[5]};if(-1!==h)e[h].references++,e[h].updater(p);else{var m=o(p,i);i.byIndex=s,e.splice(s,0,{identifier:u,updater:m,references:1})}r.push(u)}return r}function o(t,e){var n=e.domAPI(e);return n.update(t),function(e){if(e){if(e.css===t.css&&e.media===t.media&&e.sourceMap===t.sourceMap&&e.supports===t.supports&&e.layer===t.layer)return;n.update(t=e)}else n.remove()}}t.exports=function(t,o){var a=i(t=t||[],o=o||{});return function(t){t=t||[];for(var r=0;r<a.length;r++){var s=n(a[r]);e[s].references--}for(var c=i(t,o),l=0;l<a.length;l++){var d=n(a[l]);0===e[d].references&&(e[d].updater(),e.splice(d,1))}a=c}}},659:t=>{var e={};t.exports=function(t,n){var i=function(t){if(void 0===e[t]){var n=document.querySelector(t);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(t){n=null}e[t]=n}return e[t]}(t);if(!i)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");i.appendChild(n)}},540:t=>{t.exports=function(t){var e=document.createElement("style");return t.setAttributes(e,t.attributes),t.insert(e,t.options),e}},56:(t,e,n)=>{t.exports=function(t){var e=n.nc;e&&t.setAttribute("nonce",e)}},825:t=>{t.exports=function(t){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var e=t.insertStyleElement(t);return{update:function(n){!function(t,e,n){var i="";n.supports&&(i+="@supports (".concat(n.supports,") {")),n.media&&(i+="@media ".concat(n.media," {"));var o=void 0!==n.layer;o&&(i+="@layer".concat(n.layer.length>0?" ".concat(n.layer):""," {")),i+=n.css,o&&(i+="}"),n.media&&(i+="}"),n.supports&&(i+="}");var a=n.sourceMap;a&&"undefined"!=typeof btoa&&(i+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(a))))," */")),e.styleTagTransform(i,t,e.options)}(e,t,n)},remove:function(){!function(t){if(null===t.parentNode)return!1;t.parentNode.removeChild(t)}(e)}}}},494:t=>{t.exports=function(t,e){if(e.styleSheet)e.styleSheet.cssText=t;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(t))}}},156:function(t,e,n){var i=this&&this.__assign||function(){return i=Object.assign||function(t){for(var e,n=1,i=arguments.length;n<i;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},i.apply(this,arguments)},o=this&&this.__awaiter||function(t,e,n,i){return new(n||(n=Promise))((function(o,a){function r(t){try{c(i.next(t))}catch(t){a(t)}}function s(t){try{c(i.throw(t))}catch(t){a(t)}}function c(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(r,s)}c((i=i.apply(t,e||[])).next())}))},a=this&&this.__generator||function(t,e){var n,i,o,a,r={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return a={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(s){return function(c){return function(s){if(n)throw new TypeError("Generator is already executing.");for(;a&&(a=0,s[0]&&(r=0)),r;)try{if(n=1,i&&(o=2&s[0]?i.return:s[0]?i.throw||((o=i.return)&&o.call(i),0):i.next)&&!(o=o.call(i,s[1])).done)return o;switch(i=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return r.label++,{value:s[1],done:!1};case 5:r.label++,i=s[1],s=[0];continue;case 7:s=r.ops.pop(),r.trys.pop();continue;default:if(!((o=(o=r.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){r=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){r.label=s[1];break}if(6===s[0]&&r.label<o[1]){r.label=o[1],o=s;break}if(o&&r.label<o[2]){r.label=o[2],r.ops.push(s);break}o[2]&&r.ops.pop(),r.trys.pop();continue}s=e.call(t,r)}catch(t){s=[6,t],i=0}finally{n=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}};Object.defineProperty(e,"__esModule",{value:!0}),e.AuthiqaWidget=void 0;var r=n(752),s=n(92),c=n(113);n(790);var l=function(){function t(t){this.authUrls=null,this.currentAction=null,window.AuthiqaGlobalConfig&&(window.AuthiqaGlobalConfig.customization&&(t.customization=i(i({},window.AuthiqaGlobalConfig.customization),t.customization)),window.AuthiqaGlobalConfig.messages&&(t.messages=i(i({},window.AuthiqaGlobalConfig.messages),t.messages))),this.config=t,this.api=new r.ApiService(t),this.injectStyles()}return t.prototype.getAuthUrls=function(){if(!this.authUrls)throw new Error("Widget not initialized. Call initialize() first.");return this.authUrls},t.prototype.initialize=function(){var t;return o(this,void 0,void 0,(function(){var e,n,i;return a(this,(function(o){switch(o.label){case 0:return o.trys.push([0,2,,3]),[4,this.api.getOrganizationDetails()];case 1:return e=o.sent(),this.authUrls=e.authUrls,e.googleSsoConfig&&(this.googleSsoConfig=e.googleSsoConfig),null!==(t=e.domainRestrictionEnabled)&&void 0!==t&&!t||this.validateDomain(e.organizationUrl)?("signin"===this.currentAction&&this.renderSignInForm(),[3,3]):(this.showUnauthorizedError(),[2]);case 2:return n=o.sent(),console.warn("Failed to fetch organization details:",n),i=this.api.getApiBase(),this.authUrls={signin:"".concat(i,"/auth/signin"),signup:"".concat(i,"/auth/signup"),verify:"".concat(i,"/auth/verify"),reset:"".concat(i,"/auth/reset"),update:"".concat(i,"/auth/update"),resend:"".concat(i,"/auth/resend"),successful:"".concat(i,"/auth/successful")},[3,3];case 3:return[2]}}))}))},t.prototype.show=function(t){this.currentAction=t,"verify"===t?this.handleEmailVerification():"signin"===t?this.renderSignInForm():"signup"===t?this.renderSignUpForm():"reset"===t?this.renderResetPasswordForm():"update"===t?this.renderUpdatePasswordForm():"resend"===t&&this.renderResendConfirmationForm(),this.authUrls||this.initialize().catch((function(t){console.warn("Failed to fetch organization details:",t)}))},t.prototype.initializeContainer=function(){var t,e=document.getElementById(this.config.container);if(e||((e=document.createElement("div")).id=this.config.container,document.body.appendChild(e)),e.className="authiqa-container",null===(t=this.config.customization)||void 0===t?void 0:t.pageLayout){var n=this.config.customization.pageLayout;if(n.backgroundColor&&(document.body.style.backgroundColor=n.backgroundColor),n.formPosition)switch(document.body.style.display="flex",document.body.style.minHeight="100vh",n.formPosition){case"top":document.body.style.alignItems="flex-start",document.body.style.justifyContent="center";break;case"bottom":document.body.style.alignItems="flex-end",document.body.style.justifyContent="center";break;case"left":document.body.style.alignItems="center",document.body.style.justifyContent="flex-start";break;case"right":document.body.style.alignItems="center",document.body.style.justifyContent="flex-end";break;default:document.body.style.alignItems="center",document.body.style.justifyContent="center"}n.formMarginTop&&(e.style.marginTop=n.formMarginTop),n.formMarginBottom&&(e.style.marginBottom=n.formMarginBottom),n.formMarginLeft&&(e.style.marginLeft=n.formMarginLeft),n.formMarginRight&&(e.style.marginRight=n.formMarginRight)}return this.config.customization||this.config.disableStyles||("dark"===this.config.theme?document.body.setAttribute("data-theme","dark"):document.body.removeAttribute("data-theme"),"none"!==this.config.theme&&e.setAttribute("data-theme",this.config.theme||"light")),e},t.prototype.createLabeledInput=function(t,e,n,i,o){void 0===o&&(o=!0);var a=document.createElement("div");a.className="labeled-input-container",a.classList.add("authiqa-labeled-input");var r=document.createElement("label");r.setAttribute("for","authiqa-".concat(e)),r.textContent=i,r.classList.add("authiqa-label");var s=document.createElement("input");return s.setAttribute("type",t),s.setAttribute("id","authiqa-".concat(e)),s.setAttribute("name",e),s.setAttribute("placeholder",n),s.setAttribute("required",o?"true":"false"),s.classList.add("authiqa-input"),"password"===t&&s.setAttribute("minlength","6"),a.appendChild(r),a.appendChild(s),{container:a,input:s}},t.prototype.createPasswordField=function(t,e,n){var i=document.createElement("div");if(i.classList.add("authiqa-labeled-input"),n){var o=document.createElement("label");o.setAttribute("for","authiqa-".concat(e)),o.textContent=n,o.classList.add("authiqa-label"),i.appendChild(o)}var a=document.createElement("div");a.className="password-field-container",a.classList.add("authiqa-password-container");var r=document.createElement("input");r.setAttribute("type","password"),r.setAttribute("id","authiqa-".concat(e)),r.setAttribute("name",e),r.setAttribute("placeholder",t),r.setAttribute("required","true"),r.setAttribute("minlength","6"),r.classList.add("authiqa-input"),a.appendChild(r);var s=document.createElement("button");s.setAttribute("type","button"),s.classList.add("password-toggle"),s.innerHTML="👁️",s.addEventListener("click",(function(){var t=r.getAttribute("type");r.setAttribute("type","password"===t?"text":"password"),s.innerHTML="password"===t?"👁️‍🗨️":"👁️"})),a.appendChild(s);var c=document.createElement("div");c.classList.add("password-validation-container");var l=[{id:"length",text:"8+ Characters long",check:function(t){return t.length>=8}},{id:"uppercase",text:"1+ Uppercase letter",check:function(t){return/[A-Z]/.test(t)}},{id:"special",text:"1+ Special characters",check:function(t){return/[!@#$%^&*(),.?":{}|<>]/.test(t)}},{id:"number",text:"1+ Number",check:function(t){return/[0-9]/.test(t)}}];return l.forEach((function(t){var e=document.createElement("div");e.classList.add("validation-item"),e.id="validation-".concat(t.id);var n=document.createElement("span");n.classList.add("validation-dot"),n.textContent="•";var i=document.createElement("span");i.classList.add("validation-text"),i.textContent=t.text,e.appendChild(n),e.appendChild(i),c.appendChild(e)})),i.appendChild(a),i.appendChild(c),r.addEventListener("input",(function(){var t=r.value;l.forEach((function(e){var n=document.getElementById("validation-".concat(e.id));n&&(e.check(t)?n.classList.add("valid"):n.classList.remove("valid"))}))})),{container:i,input:r}},t.prototype.renderSignInForm=function(){var t,e,n,i,r,s,c,l,d,u,h,p,m,g,f,v,b,y=this,w=this.initializeContainer();w.innerHTML="";var x=document.createElement("h1");x.classList.add("authiqa-title"),x.textContent=(null===(n=null===(e=null===(t=this.config.customization)||void 0===t?void 0:t.typography)||void 0===e?void 0:e.titleText)||void 0===n?void 0:n.signinText)||"Sign in",w.appendChild(x);var A=document.createElement("form");A.classList.add("authiqa-form"),A.style.display="flex",A.style.flexDirection="column",A.style.gap="1rem";var k=this.createLabeledInput("email","email",(null===(r=null===(i=this.config.customization)||void 0===i?void 0:i.inputs)||void 0===r?void 0:r.emailPlaceholder)||"Email Address",(null===(c=null===(s=this.config.customization)||void 0===s?void 0:s.inputs)||void 0===c?void 0:c.emailLabel)||"Email"),C=k.container,S=k.input,q=this.createPasswordField((null===(d=null===(l=this.config.customization)||void 0===l?void 0:l.inputs)||void 0===d?void 0:d.passwordPlaceholder)||"Password","password",(null===(h=null===(u=this.config.customization)||void 0===u?void 0:u.inputs)||void 0===h?void 0:h.passwordLabel)||"Password"),P=q.container,T=q.input,L=this.config.resetAuthPath||(null===(p=this.authUrls)||void 0===p?void 0:p.reset)||"#",E=null===(m=this.config.customization)||void 0===m?void 0:m.navLinks,z=(null==E?void 0:E.forgotPrompt)||"Forgot Password?",M=(null==E?void 0:E.forgotLinkText)||"Reset",I=document.createElement("div");I.className="forgot-password",I.innerHTML="".concat(z,' <a href="').concat(L,'">').concat(M,"</a>");var _=document.createElement("button");_.setAttribute("type","submit"),_.classList.add("authiqa-button"),_.textContent=(null===(f=null===(g=this.config.customization)||void 0===g?void 0:g.buttons)||void 0===f?void 0:f.signinText)||"Sign In",_.style.marginTop="0.5rem",A.appendChild(C),A.appendChild(P),A.appendChild(I),A.appendChild(_),A.addEventListener("submit",(function(t){return o(y,void 0,void 0,(function(){var e,n,i,o,r,s,c,l,d,u,h,p,m,g,f,v,b=this;return a(this,(function(a){switch(a.label){case 0:t.preventDefault(),_.setAttribute("data-original-text",_.textContent||"Submit"),this.setLoadingState(_,!0,"signin"),e={email:S.value,password:T.value,parentPublicKey:this.config.publicKey},a.label=1;case 1:return a.trys.push([1,4,5,6]),[4,fetch("".concat(this.api.getApiBase(),"/auth/signin"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})];case 2:return[4,(n=a.sent()).json()];case 3:return i=a.sent(),200===n.status?i.success&&"data"in i&&((null===(u=i.data.passwordStatus)||void 0===u?void 0:u.expired)?(r=this.config.resetAuthPath||(null===(h=this.authUrls)||void 0===h?void 0:h.reset)||"",this.showMessage("Your password has expired. Please update it now.","warning",r)):void 0!==(null===(p=i.data.passwordStatus)||void 0===p?void 0:p.daysUntilExpiry)&&i.data.passwordStatus.daysUntilExpiry<=14?(o=i.data.passwordStatus.daysUntilExpiry,r=this.config.resetAuthPath||(null===(m=this.authUrls)||void 0===m?void 0:m.reset)||"",s="Your password will expire in ".concat(o," day").concat(1!==o?"s":"",". Please update it soon."),o<=3?(this.showMessage(s,"warning"),setTimeout((function(){var t,e=b.config.successAuthPath||(null===(t=b.authUrls)||void 0===t?void 0:t.successful)||"";window.location.href=e}),3e3)):(c=this.config.successAuthPath||(null===(g=this.authUrls)||void 0===g?void 0:g.successful)||"",this.showMessage(s,"warning",c))):(l=this.config.successAuthPath||(null===(f=this.authUrls)||void 0===f?void 0:f.successful)||"",this.showMessage((null===(v=this.config.messages)||void 0===v?void 0:v.signinSuccess)||"Welcome back!","success",l))):!i.success&&"error"in i?this.showMessage(i.error.message,"error"):this.showMessage("An unexpected error occurred","error"),[3,6];case 4:return d=a.sent(),console.error("Signin network error:",d),this.showMessage("Network error: Unable to connect to the server. Please check your connection and try again.","error"),[3,6];case 5:return this.setLoadingState(_,!1,"signin"),[7];case 6:return[2]}}))}))})),w.appendChild(A);var O=function(){var t;if((null===(t=y.googleSsoConfig)||void 0===t?void 0:t.enabled)&&y.googleSsoConfig.clientId&&!document.getElementById("google-signin-btn")){if(!document.getElementById("google-identity-services")){var e=document.createElement("script");e.src="https://accounts.google.com/gsi/client",e.async=!0,e.defer=!0,e.id="google-identity-services",document.head.appendChild(e)}var n=document.createElement("button");if(n.type="button",n.id="google-signin-btn",n.classList.add("authiqa-button","authiqa-google-button"),n.style.width="100%",n.innerHTML="\n                    <span style=\"display:inline-block;vertical-align:middle;margin-right:8px;\">\n                        <img src='https://www.gstatic.com/firebasejs/ui/2.0.0/images/auth/google.svg' alt='Google' style='width:20px;height:20px;vertical-align:middle;'>\n                    </span>\n                    <span style=\"vertical-align:middle;\">Sign in with Google</span>\n                ",n.style.background="#fff",n.style.color="#444",n.style.border="1px solid #ddd",n.style.borderRadius="4px",n.style.boxShadow="0 1px 2px rgba(60,64,67,.08)",n.style.fontWeight="500",n.style.fontSize="1rem",n.style.padding="0.5rem 1rem",n.style.display="flex",n.style.alignItems="center",n.style.justifyContent="center",n.style.gap="8px",n.style.margin="0.5rem 0 0 0",n.onmouseover=function(){n.style.background="#f7f7f7"},n.onmouseout=function(){n.style.background="#fff"},void 0===window._authiqaGoogleOneTapDismissed&&(window._authiqaGoogleOneTapDismissed=!1),!window._authiqaGoogleOneTapDismissed){var i=function(){window.google&&window.google.accounts?(window.google.accounts.id.initialize({client_id:y.googleSsoConfig.clientId,callback:function(t){return o(y,void 0,void 0,(function(){var e,n,i,o,r,s;return a(this,(function(a){switch(a.label){case 0:if(!(e=t.credential))return[2];a.label=1;case 1:return a.trys.push([1,4,,5]),[4,fetch("".concat(this.api.getApiBase(),"/auth/google"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({idToken:e,parentPublicKey:this.config.publicKey})})];case 2:return[4,(n=a.sent()).json()];case 3:return i=a.sent(),200===n.status&&i.success?(o=this.config.successAuthPath||(null===(r=this.authUrls)||void 0===r?void 0:r.successful)||"/",window.location.href=o):this.showMessage((null===(s=i.error)||void 0===s?void 0:s.message)||"Google sign-in failed","error"),[3,5];case 4:return a.sent(),this.showMessage("Network error during Google sign-in","error"),[3,5];case 5:return[2]}}))}))},cancel_on_tap_outside:!1,auto_select:!1}),window.google.accounts.id.prompt(),window._authiqaGoogleOneTapDismissed=!0):setTimeout(i,100)};i()}var r=document.createElement("div");r.id="google-button-container",r.style.margin="0.5rem 0 0 0",r.style.width="100%";var s=function(){window.google&&window.google.accounts?(window.google.accounts.id.initialize({client_id:y.googleSsoConfig.clientId,callback:function(t){return o(y,void 0,void 0,(function(){var e,n,i,o,r,s;return a(this,(function(a){switch(a.label){case 0:if(!(e=t.credential))return[2];a.label=1;case 1:return a.trys.push([1,4,,5]),[4,fetch("".concat(this.api.getApiBase(),"/auth/google"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({idToken:e,parentPublicKey:this.config.publicKey})})];case 2:return[4,(n=a.sent()).json()];case 3:return i=a.sent(),200===n.status&&i.success?(o=this.config.successAuthPath||(null===(r=this.authUrls)||void 0===r?void 0:r.successful)||"/",window.location.href=o):this.showMessage((null===(s=i.error)||void 0===s?void 0:s.message)||"Google sign-in failed","error"),[3,5];case 4:return a.sent(),this.showMessage("Network error during Google sign-in","error"),[3,5];case 5:return[2]}}))}))},ux_mode:"popup"}),window.google.accounts.id.renderButton(r,{theme:"outline",size:"large",text:"signin_with",shape:"rectangular",logo_alignment:"left",width:"100%"}),setTimeout((function(){var t=r.querySelector("iframe");t&&(t.style.width="100%",t.style.minWidth="100%",t.style.maxWidth="100%");var e=r.querySelector("div");e&&(e.style.width="100%")}),100)):setTimeout(s,100)};n.style.display="none",s(),A.insertBefore(r,_.nextSibling)}};if(O(),!(null===(v=this.googleSsoConfig)||void 0===v?void 0:v.enabled)||!this.googleSsoConfig.clientId){var U=setInterval((function(){var t;(null===(t=y.googleSsoConfig)||void 0===t?void 0:t.enabled)&&y.googleSsoConfig.clientId&&!document.getElementById("google-signin-btn")&&(O(),clearInterval(U))}),200);setTimeout((function(){return clearInterval(U)}),5e3)}var N=(null===(b=this.authUrls)||void 0===b?void 0:b.signup)||"#",j=(null==E?void 0:E.signupPrompt)||"Don't have an account?",R=(null==E?void 0:E.signupLinkText)||"Sign Up",B=document.createElement("div");B.className="alternate-action",B.innerHTML="".concat(j,' <a href="').concat(N,'">').concat(R,"</a>"),A.appendChild(B)},t.prototype.renderSignUpForm=function(){var t,e,n,i,r,s,c,l,d,u,h,p,m,g,f,v,b,y,w,x,A,k,C=this,S=this.initializeContainer();S.innerHTML="";var q=null===(t=this.config.customization)||void 0===t?void 0:t.navLinks,P=document.createElement("h1");P.classList.add("authiqa-title"),P.textContent=(null===(i=null===(n=null===(e=this.config.customization)||void 0===e?void 0:e.typography)||void 0===n?void 0:n.titleText)||void 0===i?void 0:i.signupText)||"Sign up",S.appendChild(P);var T=document.createElement("form");T.classList.add("authiqa-form"),T.style.display="flex",T.style.flexDirection="column",T.style.gap="1rem";var L=this.createLabeledInput("text","username",(null===(s=null===(r=this.config.customization)||void 0===r?void 0:r.inputs)||void 0===s?void 0:s.usernamePlaceholder)||"Username",(null===(l=null===(c=this.config.customization)||void 0===c?void 0:c.inputs)||void 0===l?void 0:l.usernameLabel)||"Username"),E=L.container,z=L.input,M=this.createLabeledInput("email","email",(null===(u=null===(d=this.config.customization)||void 0===d?void 0:d.inputs)||void 0===u?void 0:u.emailPlaceholder)||"Email Address",(null===(p=null===(h=this.config.customization)||void 0===h?void 0:h.inputs)||void 0===p?void 0:p.emailLabel)||"Email"),I=M.container,_=M.input,O=this.createPasswordField((null===(g=null===(m=this.config.customization)||void 0===m?void 0:m.inputs)||void 0===g?void 0:g.passwordPlaceholder)||"Password","password",(null===(v=null===(f=this.config.customization)||void 0===f?void 0:f.inputs)||void 0===v?void 0:v.passwordLabel)||"Password"),U=O.container,N=O.input;T.appendChild(E),T.appendChild(I),T.appendChild(U);var j=document.createElement("div");j.classList.add("terms-container"),j.style.display="flex",j.style.alignItems="flex-start",j.style.marginBottom="1rem";var R=document.createElement("input");R.setAttribute("type","checkbox"),R.setAttribute("id","terms"),R.setAttribute("name","terms"),R.setAttribute("required","required"),R.style.marginTop="0.25rem",R.style.marginRight="0.5rem";var B=document.createElement("label");B.setAttribute("for","terms"),B.style.flex="1",B.style.margin="0",B.style.padding="0",B.style.color="#525252",B.style.fontSize="0.875rem",B.style.lineHeight="1.4";var D=(null===(y=null===(b=this.config.customization)||void 0===b?void 0:b.typography)||void 0===y?void 0:y.termsText)||{agreePrefix:"I agree with the",andConnector:"and",defaultPrefix:"default",linkText:{terms:"Terms of Service",privacy:"Privacy Policy",notifications:"Notification Settings"}},F=D.agreePrefix,G=D.andConnector,H=D.defaultPrefix,V=D.linkText;B.innerHTML="".concat(F,' <a href="').concat(this.config.termsAndConditions||"#",'">').concat(V.terms,'</a> <a href="').concat(this.config.privacy||"#",'">').concat(V.privacy,"</a> ").concat(G," ").concat(H,' <a href="').concat(this.config.notificationSettings||"#",'">').concat(V.notifications,"</a>."),j.appendChild(R),j.appendChild(B);var K=document.createElement("button");K.setAttribute("type","submit"),K.classList.add("authiqa-button");var W=(null===(x=null===(w=this.config.customization)||void 0===w?void 0:w.buttons)||void 0===x?void 0:x.signupText)||"Create Account";K.textContent=W,T.appendChild(j),T.appendChild(K),T.addEventListener("submit",(function(t){return o(C,void 0,void 0,(function(){var e,n,i,o,r,s,c,l,d,u,h;return a(this,(function(a){switch(a.label){case 0:if(t.preventDefault(),!R.checked)return this.showMessage("Please accept the terms and conditions","error"),[2];if(!(e=this.validatePassword(N.value)).isValid&&e.error)return this.showMessage("".concat(e.error.message," (").concat(e.error.code,")"),"error"),[2];K.setAttribute("data-original-text",K.textContent||"Submit"),this.setLoadingState(K,!0,"signup"),n={username:z.value,email:_.value,password:N.value,parentPublicKey:this.config.publicKey,verifyAuthPath:this.config.verifyAuthPath},a.label=1;case 1:return a.trys.push([1,4,5,6]),[4,fetch("".concat(this.api.getApiBase(),"/auth/signup"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(n)})];case 2:return[4,(i=a.sent()).json()];case 3:switch(o=a.sent(),i.status){case 200:o.success&&o.data&&(r=this.config.resendAuthPath||(null===(h=this.authUrls)||void 0===h?void 0:h.resend),this.showMessage("Successfully signed up! Please check your email for verification link","success","".concat(r,"?email=").concat(encodeURIComponent(_.value))));break;case 409:switch((s=o.error).code){case"EMAIL_ALREADY_EXISTS":case"USERNAME_ALREADY_EXISTS":case"DUPLICATE_EMAIL_USERNAME_COMBO":this.showMessage("".concat(s.message," (").concat(s.code,")"),"error");break;default:this.showMessage("".concat(s.message),"error")}break;case 400:switch((c=o.error).code){case"MISSING_REQUEST_BODY":case"MISSING_REQUIRED_FIELDS":case"INVALID_EMAIL_FORMAT":case"INVALID_PASSWORD_FORMAT":case"INVALID_USERNAME_FORMAT":case"MISSING_PARENT_PUBLIC_KEY":this.showMessage("".concat(c.message," (").concat(c.code,")"),"error");break;default:this.showMessage("".concat(c.message),"error")}break;case 401:"INVALID_PARENT_PUBLIC_KEY"===(l=o.error).code?this.showMessage("".concat(l.message," (").concat(l.code,")"),"error"):this.showMessage("".concat(l.message),"error");break;case 403:"PARENT_ACCOUNT_INACTIVE"===(d=o.error).code?this.showMessage("".concat(d.message," (").concat(d.code,")"),"error"):this.showMessage("".concat(d.message),"error");break;case 500:this.showMessage("An internal server error occurred. Please try again later.","error");break;default:this.showMessage("An unexpected error occurred. Please try again.","error")}return[3,6];case 4:return u=a.sent(),console.error("Signup network error:",u),this.showMessage("Network error: Unable to connect to the server. Please check your connection and try again.","error"),[3,6];case 5:return this.setLoadingState(K,!1,"signup"),[7];case 6:return[2]}}))}))})),S.appendChild(T);var J=function(){var t;if((null===(t=C.googleSsoConfig)||void 0===t?void 0:t.enabled)&&C.googleSsoConfig.clientId&&!document.getElementById("google-signup-btn")){if(!document.getElementById("google-identity-services")){var e=document.createElement("script");e.src="https://accounts.google.com/gsi/client",e.async=!0,e.defer=!0,e.id="google-identity-services",document.head.appendChild(e)}var n=document.createElement("button");if(n.type="button",n.id="google-signup-btn",n.classList.add("authiqa-button","authiqa-google-button"),n.style.width="100%",n.innerHTML="\n                        <span style=\"display:inline-block;vertical-align:middle;margin-right:8px;\">\n                            <img src='https://www.gstatic.com/firebasejs/ui/2.0.0/images/auth/google.svg' alt='Google' style='width:20px;height:20px;vertical-align:middle;'>\n                        </span>\n                        <span style=\"vertical-align:middle;\">Sign up with Google</span>\n                    ",n.style.background="#fff",n.style.color="#444",n.style.border="1px solid #ddd",n.style.borderRadius="4px",n.style.boxShadow="0 1px 2px rgba(60,64,67,.08)",n.style.fontWeight="500",n.style.fontSize="1rem",n.style.padding="0.5rem 1rem",n.style.display="flex",n.style.alignItems="center",n.style.justifyContent="center",n.style.gap="8px",n.style.margin="0.5rem 0 0 0",n.onmouseover=function(){n.style.background="#f7f7f7"},n.onmouseout=function(){n.style.background="#fff"},void 0===window._authiqaGoogleOneTapDismissed&&(window._authiqaGoogleOneTapDismissed=!1),!window._authiqaGoogleOneTapDismissed){var i=function(){window.google&&window.google.accounts?(window.google.accounts.id.initialize({client_id:C.googleSsoConfig.clientId,callback:function(t){return o(C,void 0,void 0,(function(){var e,n,i,o,r,s;return a(this,(function(a){switch(a.label){case 0:if(!(e=t.credential))return[2];a.label=1;case 1:return a.trys.push([1,4,,5]),[4,fetch("".concat(this.api.getApiBase(),"/auth/google"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({idToken:e,parentPublicKey:this.config.publicKey})})];case 2:return[4,(n=a.sent()).json()];case 3:return i=a.sent(),200===n.status&&i.success?(o=this.config.successAuthPath||(null===(r=this.authUrls)||void 0===r?void 0:r.successful)||"/",window.location.href=o):this.showMessage((null===(s=i.error)||void 0===s?void 0:s.message)||"Google sign-up failed","error"),[3,5];case 4:return a.sent(),this.showMessage("Network error during Google sign-up","error"),[3,5];case 5:return[2]}}))}))},cancel_on_tap_outside:!1,auto_select:!1}),window.google.accounts.id.prompt(),window._authiqaGoogleOneTapDismissed=!0):setTimeout(i,100)};i()}var r=document.createElement("div");r.id="google-signup-button-container",r.style.margin="0.5rem 0 0 0",r.style.width="100%";var s=function(){window.google&&window.google.accounts?(window.google.accounts.id.initialize({client_id:C.googleSsoConfig.clientId,callback:function(t){return o(C,void 0,void 0,(function(){var e,n,i,o,r,s;return a(this,(function(a){switch(a.label){case 0:if(!(e=t.credential))return[2];a.label=1;case 1:return a.trys.push([1,4,,5]),[4,fetch("".concat(this.api.getApiBase(),"/auth/google"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({idToken:e,parentPublicKey:this.config.publicKey})})];case 2:return[4,(n=a.sent()).json()];case 3:return i=a.sent(),200===n.status&&i.success?(o=this.config.successAuthPath||(null===(r=this.authUrls)||void 0===r?void 0:r.successful)||"/",window.location.href=o):this.showMessage((null===(s=i.error)||void 0===s?void 0:s.message)||"Google sign-up failed","error"),[3,5];case 4:return a.sent(),this.showMessage("Network error during Google sign-up","error"),[3,5];case 5:return[2]}}))}))},ux_mode:"popup"}),window.google.accounts.id.renderButton(r,{theme:"outline",size:"large",text:"signup_with",shape:"rectangular",logo_alignment:"left",width:"100%"}),setTimeout((function(){var t=r.querySelector("iframe");t&&(t.style.width="100%",t.style.minWidth="100%",t.style.maxWidth="100%");var e=r.querySelector("div");e&&(e.style.width="100%")}),100)):setTimeout(s,100)};n.style.display="none",s(),T.insertBefore(r,K.nextSibling)}};if(J(),!(null===(A=this.googleSsoConfig)||void 0===A?void 0:A.enabled)||!this.googleSsoConfig.clientId){var Y=setInterval((function(){var t;(null===(t=C.googleSsoConfig)||void 0===t?void 0:t.enabled)&&C.googleSsoConfig.clientId&&!document.getElementById("google-signup-btn")&&(J(),clearInterval(Y))}),200);setTimeout((function(){return clearInterval(Y)}),5e3)}var X=this.config.signinAuthPath||(null===(k=this.authUrls)||void 0===k?void 0:k.signin)||"#",Z=(null==q?void 0:q.signinPrompt)||"Already have an account?",$=(null==q?void 0:q.signinLinkText)||"Sign In",Q=document.createElement("div");Q.className="alternate-action",Q.innerHTML="".concat(Z,' <a href="').concat(X,'">').concat($,"</a>"),T.appendChild(Q)},t.prototype.validatePassword=function(t){var e=t.length>=8,n=/[A-Z]/.test(t),i=/[0-9]/.test(t),o=/[!@#$%^&*(),.?":{}|<>]/.test(t);if(!(e&&n&&i&&o)){var a="Password must contain:";return e||(a+=" at least 8 characters,"),n||(a+=" at least one uppercase letter,"),i||(a+=" at least one number,"),o||(a+=" at least one special character,"),{isValid:!1,error:{code:"INVALID_PASSWORD_FORMAT",message:a=a.replace(/,$/,"")}}}return{isValid:!0}},t.prototype.renderResetPasswordForm=function(){var t,e,n,i,r,s,c,l,d,u,h,p,m,g,f=this,v=this.initializeContainer();v.innerHTML="";var b=document.createElement("h1");b.classList.add("authiqa-title"),b.textContent=(null===(n=null===(e=null===(t=this.config.customization)||void 0===t?void 0:t.typography)||void 0===e?void 0:e.titleText)||void 0===n?void 0:n.resetText)||"Reset Password",(null===(s=null===(r=null===(i=this.config.customization)||void 0===i?void 0:i.typography)||void 0===r?void 0:r.subtitleText)||void 0===s?void 0:s.resetText)&&b.setAttribute("data-subtitle",this.config.customization.typography.subtitleText.resetText),v.appendChild(b);var y=document.createElement("form");y.classList.add("authiqa-form"),y.style.display="flex",y.style.flexDirection="column",y.style.gap="1rem";var w=this.createLabeledInput("email","email",(null===(l=null===(c=this.config.customization)||void 0===c?void 0:c.inputs)||void 0===l?void 0:l.emailPlaceholder)||"Email Address",(null===(u=null===(d=this.config.customization)||void 0===d?void 0:d.inputs)||void 0===u?void 0:u.emailLabel)||"Email"),x=w.container,A=w.input;y.appendChild(x);var k=document.createElement("button");k.setAttribute("type","submit"),k.classList.add("authiqa-button"),k.textContent=(null===(p=null===(h=this.config.customization)||void 0===h?void 0:h.buttons)||void 0===p?void 0:p.resetText)||"Reset Password",y.appendChild(k);var C=this.config.signinAuthPath||(null===(m=this.authUrls)||void 0===m?void 0:m.signin)||"#",S=null===(g=this.config.customization)||void 0===g?void 0:g.navLinks,q=(null==S?void 0:S.backToSigninPrompt)||"Back to Sign In?",P=document.createElement("div");P.className="alternate-action",P.innerHTML="".concat(q,' <a href="').concat(C,'">Sign In</a>'),y.appendChild(P),y.addEventListener("submit",(function(t){return o(f,void 0,void 0,(function(){var e,n,i,o,r;return a(this,(function(a){switch(a.label){case 0:t.preventDefault(),this.setLoadingState(k,!0,"reset"),e={email:A.value,parentPublicKey:this.config.publicKey,updatePasswordPath:this.config.updatePasswordPath},a.label=1;case 1:return a.trys.push([1,4,5,6]),[4,fetch("".concat(this.api.getApiBase(),"/auth/reset-password"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})];case 2:return[4,(n=a.sent()).json()];case 3:return i=a.sent(),200===n.status?i.success&&i.data&&this.showMessage((null===(r=this.config.messages)||void 0===r?void 0:r.resetSuccess)||i.data.message,"success"):!i.success&&i.error?this.showMessage(i.error.message,"error"):this.showMessage("An unexpected error occurred","error"),[3,6];case 4:return o=a.sent(),console.error("Reset password network error:",o),this.showMessage("Unable to connect to the server","error"),[3,6];case 5:return this.setLoadingState(k,!1,"reset"),[7];case 6:return[2]}}))}))})),v.appendChild(y)},t.prototype.renderUpdatePasswordForm=function(){var t,e,n,i,r,s,c,l,d,u,h,p,m,g=this,f=this.initializeContainer();f.innerHTML="";var v=document.createElement("h1");v.classList.add("authiqa-title"),v.textContent=(null===(n=null===(e=null===(t=this.config.customization)||void 0===t?void 0:t.typography)||void 0===e?void 0:e.titleText)||void 0===n?void 0:n.updateText)||"Update Password",f.appendChild(v);var b=new URLSearchParams(window.location.search).get("token"),y=document.createElement("form");if(y.classList.add("authiqa-form"),y.style.display="flex",y.style.flexDirection="column",y.style.gap="1rem",b){var w=document.createElement("input");w.setAttribute("type","hidden"),w.setAttribute("name","token"),w.value=b,y.appendChild(w)}else console.warn("No token found in URL - password reset may fail");var x=document.createElement("div");x.classList.add("authiqa-labeled-input");var A=document.createElement("label");A.setAttribute("for","authiqa-newPassword"),A.textContent=(null===(r=null===(i=this.config.customization)||void 0===i?void 0:i.inputs)||void 0===r?void 0:r.passwordLabel)||"New Password",A.classList.add("authiqa-label"),x.appendChild(A);var k=document.createElement("div");k.className="password-field-container",k.classList.add("authiqa-password-container");var C=document.createElement("input");C.setAttribute("type","password"),C.setAttribute("id","authiqa-newPassword"),C.setAttribute("name","newPassword"),C.setAttribute("placeholder",(null===(c=null===(s=this.config.customization)||void 0===s?void 0:s.inputs)||void 0===c?void 0:c.passwordPlaceholder)||"New Password"),C.setAttribute("required","true"),C.setAttribute("minlength","6"),C.classList.add("authiqa-input"),k.appendChild(C);var S=document.createElement("button");S.setAttribute("type","button"),S.classList.add("password-toggle"),S.innerHTML="👁️",S.addEventListener("click",(function(){var t=C.getAttribute("type");C.setAttribute("type","password"===t?"text":"password"),S.innerHTML="password"===t?"👁️‍🗨️":"👁️"})),k.appendChild(S),x.appendChild(k);var q=document.createElement("div");q.classList.add("authiqa-labeled-input");var P=document.createElement("label");P.setAttribute("for","authiqa-confirmPassword"),P.textContent=(null===(d=null===(l=this.config.customization)||void 0===l?void 0:l.inputs)||void 0===d?void 0:d.confirmPasswordLabel)||"Confirm Password",P.classList.add("authiqa-label"),q.appendChild(P);var T=document.createElement("div");T.className="password-field-container",T.classList.add("authiqa-password-container");var L=document.createElement("input");L.setAttribute("type","password"),L.setAttribute("id","authiqa-confirmPassword"),L.setAttribute("name","confirmPassword"),L.setAttribute("placeholder",(null===(h=null===(u=this.config.customization)||void 0===u?void 0:u.inputs)||void 0===h?void 0:h.confirmPasswordPlaceholder)||"Confirm Password"),L.setAttribute("required","true"),L.classList.add("authiqa-input"),T.appendChild(L);var E=document.createElement("button");E.setAttribute("type","button"),E.classList.add("password-toggle"),E.innerHTML="👁️",E.addEventListener("click",(function(){var t=L.getAttribute("type");L.setAttribute("type","password"===t?"text":"password"),E.innerHTML="password"===t?"👁️‍🗨️":"👁️"})),T.appendChild(E),q.appendChild(T);var z=document.createElement("div");z.classList.add("password-validation-container");var M=[{id:"length",text:"8+ Characters long",check:function(t){return t.length>=8}},{id:"uppercase",text:"1+ Uppercase letter",check:function(t){return/[A-Z]/.test(t)}},{id:"special",text:"1+ Special characters",check:function(t){return/[!@#$%^&*(),.?":{}|<>]/.test(t)}},{id:"number",text:"1+ Number",check:function(t){return/[0-9]/.test(t)}}];M.forEach((function(t){var e=document.createElement("div");e.classList.add("validation-item"),e.id="validation-".concat(t.id);var n=document.createElement("span");n.classList.add("validation-dot"),n.textContent="•";var i=document.createElement("span");i.classList.add("validation-text"),i.textContent=t.text,e.appendChild(n),e.appendChild(i),z.appendChild(e)})),y.appendChild(x),y.appendChild(q),y.appendChild(z),C.addEventListener("input",(function(){var t=C.value;M.forEach((function(e){var n=document.getElementById("validation-".concat(e.id));n&&(e.check(t)?n.classList.add("valid"):n.classList.remove("valid"))}))}));var I=function(){L.value&&(C.value!==L.value?L.setCustomValidity("Passwords do not match"):L.setCustomValidity(""))};C.addEventListener("input",I),L.addEventListener("input",I);var _=document.createElement("button");_.setAttribute("type","submit"),_.classList.add("authiqa-button"),_.textContent=(null===(m=null===(p=this.config.customization)||void 0===p?void 0:p.buttons)||void 0===m?void 0:m.updateText)||"Update Password",y.appendChild(_),y.addEventListener("submit",(function(t){return o(g,void 0,void 0,(function(){var e,n,i,o,r,s;return a(this,(function(a){switch(a.label){case 0:if(t.preventDefault(),C.value!==L.value)return this.showMessage("Passwords do not match","error"),[2];this.setLoadingState(_,!0,"update"),e={token:b,password:C.value},a.label=1;case 1:return a.trys.push([1,4,5,6]),[4,fetch("".concat(this.api.getApiBase(),"/auth/update-password"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})];case 2:return[4,(n=a.sent()).json()];case 3:return i=a.sent(),200===n.status?i.success&&i.data&&(o=this.config.signinAuthPath||(null===(r=this.authUrls)||void 0===r?void 0:r.signin),this.showMessage((null===(s=this.config.messages)||void 0===s?void 0:s.updateSuccess)||"Password updated successfully!","success",o)):!i.success&&i.error?this.showMessage(i.error.message,"error"):this.showMessage("An unexpected error occurred","error"),[3,6];case 4:return a.sent(),this.showMessage("Network error: Unable to connect to the server","error"),[3,6];case 5:return this.setLoadingState(_,!1,"update"),[7];case 6:return[2]}}))}))})),f.appendChild(y)},t.prototype.renderResendConfirmationForm=function(){var t,e,n,i,r,s,c,l,d,u,h,p=this,m=this.initializeContainer();m.innerHTML="";var g=document.createElement("h1");g.classList.add("authiqa-title"),g.textContent=(null===(n=null===(e=null===(t=this.config.customization)||void 0===t?void 0:t.typography)||void 0===e?void 0:e.titleText)||void 0===n?void 0:n.resendText)||"Resend Confirmation",m.appendChild(g);var f=document.createElement("form");f.classList.add("authiqa-form"),f.style.display="flex",f.style.flexDirection="column",f.style.gap="1rem";var v=new URLSearchParams(window.location.search).get("email"),b=this.createLabeledInput("email","email",(null===(r=null===(i=this.config.customization)||void 0===i?void 0:i.inputs)||void 0===r?void 0:r.emailPlaceholder)||"Email Address",(null===(c=null===(s=this.config.customization)||void 0===s?void 0:s.inputs)||void 0===c?void 0:c.emailLabel)||"Email"),y=b.container,w=b.input;v&&(w.value=v);var x=document.createElement("button");x.setAttribute("type","submit"),x.classList.add("authiqa-button"),x.textContent=(null===(d=null===(l=this.config.customization)||void 0===l?void 0:l.buttons)||void 0===d?void 0:d.resendText)||"Resend Confirmation",f.appendChild(y),f.appendChild(x);var A=this.config.signinAuthPath||(null===(u=this.authUrls)||void 0===u?void 0:u.signin)||"#",k=null===(h=this.config.customization)||void 0===h?void 0:h.navLinks,C=(null==k?void 0:k.backToSigninPrompt)||"Back to Sign In?",S=document.createElement("div");S.className="alternate-action",S.innerHTML="".concat(C,' <a href="').concat(A,'">Sign In</a>'),f.appendChild(S),f.addEventListener("submit",(function(t){return o(p,void 0,void 0,(function(){var e,n,i,o;return a(this,(function(a){switch(a.label){case 0:t.preventDefault(),this.setLoadingState(x,!0,"resend"),e={email:w.value,parentPublicKey:this.config.publicKey,verifyAuthPath:this.config.verifyAuthPath},a.label=1;case 1:return a.trys.push([1,4,5,6]),[4,fetch("".concat(this.api.getApiBase(),"/auth/request-new-confirmation"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})];case 2:return[4,(n=a.sent()).json()];case 3:return i=a.sent(),200===n.status?i.success&&i.data&&this.showMessage((null===(o=this.config.messages)||void 0===o?void 0:o.resendSuccess)||i.data.message,"success"):!i.success&&i.error?this.showMessage(i.error.message,"error"):this.showMessage("An unexpected error occurred","error"),[3,6];case 4:return a.sent(),this.showMessage("Network error: Unable to connect to the server","error"),[3,6];case 5:return this.setLoadingState(x,!1,"resend"),[7];case 6:return[2]}}))}))})),m.appendChild(f)},t.prototype.renderVerificationStatus=function(t,e){var n,i,o=this.initializeContainer();o.innerHTML="";var a=document.createElement("div");a.className="verification-status";var r=document.createElement("h1");r.textContent="Email Verification";var s=document.createElement("div");if("loading"===t){var c=document.createElement("div");c.className="verification-loader",s.appendChild(c),e=(null===(n=this.config.messages)||void 0===n?void 0:n.verificationLoading)||e}else{var l=document.createElement("div");l.className="verification-icon ".concat(t),l.innerHTML="success"===t?"✓":"✕",s.appendChild(l),"success"===t&&(e=(null===(i=this.config.messages)||void 0===i?void 0:i.verificationSuccess)||e)}var d=document.createElement("p");d.textContent=e,a.appendChild(r),a.appendChild(s),a.appendChild(d),o.appendChild(a)},t.prototype.handleEmailVerification=function(){var t,e,n;return o(this,void 0,void 0,(function(){var i,o,r,s,c,l,d,u,h;return a(this,(function(a){switch(a.label){case 0:if(i=new URLSearchParams(window.location.search),!(o=i.get("token")))return this.renderVerificationStatus("error","Invalid verification token (INVALID_TOKEN)"),[2];this.renderVerificationStatus("loading",(null===(t=this.config.messages)||void 0===t?void 0:t.verificationLoading)||"Verifying your email address..."),a.label=1;case 1:return a.trys.push([1,4,,5]),[4,fetch("".concat(this.api.getApiBase(),"/auth/confirm-email?token=").concat(encodeURIComponent(o)),{headers:{"X-Public-Key":this.config.publicKey,"Content-Type":"application/json"}})];case 2:return[4,(r=a.sent()).json()];case 3:switch(s=a.sent(),r.status){case 200:s.success&&s.data&&(this.renderVerificationStatus("success",(null===(e=this.config.messages)||void 0===e?void 0:e.verificationSuccess)||s.data.message||"Email verified successfully"),c=this.config.signinAuthPath||(null===(n=this.authUrls)||void 0===n?void 0:n.signin),setTimeout((function(){window.location.href=c||"/"}),2e3));break;case 400:l=s.error,this.renderVerificationStatus("error","".concat(l.message," (").concat(l.code,")"));break;case 404:d=s.error,this.renderVerificationStatus("error","".concat(d.message," (").concat(d.code,")"));break;case 500:u=s.error,this.renderVerificationStatus("error","".concat(u.message," (").concat(u.code,")"));break;default:this.renderVerificationStatus("error","An unexpected error occurred. Please try again.")}return[3,5];case 4:return h=a.sent(),console.error("Error during email verification:",h),this.renderVerificationStatus("error","Network error: Unable to connect to the server. Please check your connection and try again."),[3,5];case 5:return[2]}}))}))},t.prototype.showMessage=function(t,e,n){var i,o,a,r=document.createElement("div");r.classList.add("authiqa-message"),r.classList.add("authiqa-message-".concat(e)),"success"===e?r.style.backgroundColor="#4caf50":"error"===e?r.style.backgroundColor="#f44336":"warning"===e&&(r.style.backgroundColor="#ff9800");var s=n;if("success"===e&&!n)switch(this.currentAction){case"signin":s=this.config.successAuthPath||(null===(i=this.authUrls)||void 0===i?void 0:i.successful);break;case"update":s=this.config.signinAuthPath||(null===(o=this.authUrls)||void 0===o?void 0:o.signin);break;case"signup":s=this.config.resendAuthPath||(null===(a=this.authUrls)||void 0===a?void 0:a.resend)}var c=document.querySelector(".authiqa-message");c&&c.remove(),r.textContent=t,document.body.appendChild(r),r.classList.add("show");var l=2e3;"error"===e?l=7e3:"success"===e?l=4e3:"warning"===e&&(l=5e3),setTimeout((function(){r.classList.remove("show"),setTimeout((function(){r.remove(),s&&(window.location.href=s)}),300)}),l)},t.prototype.getCustomSuccessMessage=function(t){var e,n,i,o,a;switch(this.currentAction){case"signin":return(null===(e=this.config.messages)||void 0===e?void 0:e.signinSuccess)||t;case"signup":return(null===(n=this.config.messages)||void 0===n?void 0:n.signupSuccess)||t;case"reset":return(null===(i=this.config.messages)||void 0===i?void 0:i.resetSuccess)||t;case"update":return(null===(o=this.config.messages)||void 0===o?void 0:o.updateSuccess)||t;case"resend":return(null===(a=this.config.messages)||void 0===a?void 0:a.resendSuccess)||t;default:return t}},t.prototype.setLoadingState=function(t,e,n){if(e){var i=t.textContent||"Submit";t.setAttribute("data-original-text",i);var o=this.getCustomLoadingMessage(n)||"Please wait...";t.textContent=o}else t.textContent=t.getAttribute("data-original-text")||"Submit"},t.prototype.getCustomLoadingMessage=function(t){var e,n,i,o,a;switch(t){case"signin":return null===(e=this.config.messages)||void 0===e?void 0:e.signinLoading;case"signup":return null===(n=this.config.messages)||void 0===n?void 0:n.signupLoading;case"reset":return null===(i=this.config.messages)||void 0===i?void 0:i.resetLoading;case"update":return null===(o=this.config.messages)||void 0===o?void 0:o.updateLoading;case"resend":return null===(a=this.config.messages)||void 0===a?void 0:a.resendLoading;default:return}},t.prototype.injectStyles=function(){if(!this.config.disableStyles){var t=document.getElementById("authiqa-styles");t&&t.remove();var e=document.createElement("style");e.id="authiqa-styles";var n=this.config.theme&&"none"!==this.config.theme?this.config.theme:"light",i="";i+=(0,s.getStyleContent)(n);var o=(0,s.getComponentStyles)(n);i+="\n            /* Modal Styles */\n            .authiqa-modal-overlay {\n                ".concat(Object.entries(o.modal.overlay).map((function(t){var e=t[0],n=t[1];return"".concat(e,": ").concat(n,";")})).join("\n"),"\n            }\n            .authiqa-modal-container {\n                ").concat(Object.entries(o.modal.container).map((function(t){var e=t[0],n=t[1];return"".concat(e,": ").concat(n,";")})).join("\n"),"\n            }\n            .authiqa-iframe {\n                ").concat(Object.entries(o.iframe).map((function(t){var e=t[0],n=t[1];return"".concat(e,": ").concat(n,";")})).join("\n"),"\n            }\n            /* Message Styles */\n            .authiqa-message {\n                ").concat(Object.entries(o.message).map((function(t){var e=t[0],n=t[1];return"".concat(e,": ").concat(n,";")})).join("\n"),"\n            }\n            .authiqa-message-success {\n                ").concat(Object.entries(o.messageSuccess).map((function(t){var e=t[0],n=t[1];return"".concat(e,": ").concat(n,";")})).join("\n"),"\n            }\n            .authiqa-message-error {\n                ").concat(Object.entries(o.messageError).map((function(t){var e=t[0],n=t[1];return"".concat(e,": ").concat(n,";")})).join("\n"),"\n            }\n            .authiqa-message-show {\n                ").concat(Object.entries(o.messageShow).map((function(t){var e=t[0],n=t[1];return"".concat(e,": ").concat(n,";")})).join("\n"),"\n            }\n        "),this.config.customization&&(i+=new c.StyleGenerator(this.config.customization).generateStyles(),i+=(0,s.generatePageLayoutStyles)(this.config),i+=(0,s.generateTermsContainerStyles)(this.config)),e.textContent=i,document.head.appendChild(e)}},t.prototype.generateCustomStyles=function(t){var e=t.colors,n=t.typography,i=t.layout,o=t.buttons;return"\n            .authiqa-container {\n                background-color: ".concat(e.background,";\n                padding: ").concat(i.padding,";\n                margin: ").concat(i.margin,";\n                border-radius: ").concat(i.borderRadius,";\n                max-width: ").concat(i.maxWidth,";\n                font-family: ").concat(n.fontFamily,";\n            }\n\n            .authiqa-container h1 {\n                color: ").concat(n.titleColor,";\n                font-size: ").concat(n.titleSize,";\n            }\n\n            .authiqa-container input {\n                background-color: ").concat(e.inputBackground,";\n                color: ").concat(e.inputText,";\n                border: 1px solid ").concat(e.borderColor,";\n            }\n\n            .authiqa-container button {\n                background-color: ").concat(e.buttonBackground,";\n                color: ").concat(e.buttonText,";\n                height: ").concat(o.height||"40px",";\n                width: ").concat(o.width||"100%",";\n                border-radius: ").concat(o.borderRadius,";\n            }\n        ")},t.prototype.updateTheme=function(t){if(!this.config.disableStyles)if(document.getElementById("authiqa-styles")){"dark"===t?document.body.setAttribute("data-theme","dark"):document.body.removeAttribute("data-theme");var e=document.getElementById(this.config.container);e&&e.setAttribute("data-theme",t)}else this.injectStyles()},t.prototype.cleanup=function(){var t=document.getElementById("authiqa-styles");t&&t.remove(),document.body.style.backgroundColor="",document.body.style.display="",document.body.style.minHeight="",document.body.style.alignItems="",document.body.style.justifyContent="",document.body.removeAttribute("data-theme");var e=document.getElementById(this.config.container);e&&(e.removeAttribute("data-theme"),e.style.marginTop="",e.style.marginBottom="",e.style.marginLeft="",e.style.marginRight="")},t.prototype.handleApiError=function(t){var e;(null===(e=null==t?void 0:t.error)||void 0===e?void 0:e.message)?this.showMessage(t.error.message,"error"):t instanceof Error?this.showMessage("Unable to connect to the server","error"):this.showMessage("An unexpected error occurred","error")},t.prototype.validateDomain=function(t){if(this.isDevelopmentMode())return!0;var e;try{e=new URL(t).hostname}catch(e){return console.error("Invalid organization URL:",t),!1}var n=window.location.hostname;return n===e||n.endsWith("."+e)||"authiqa.com"===n||"www.authiqa.com"===n},t.prototype.isDevelopmentMode=function(){var t=document.querySelector("script[data-public-key]");return!!t&&"true"===t.getAttribute("authiqa--dev-data-mode")},t.prototype.showUnauthorizedError=function(){var t=document.getElementById(this.config.container);if(t){t.innerHTML="";var e=document.createElement("div");e.className="authiqa-error-container";var n=document.createElement("h2");n.textContent="Unauthorized Domain",n.style.color="#e74c3c";var i=document.createElement("p");i.textContent="This widget can only be used on authorized domains. Please visit Authiqa and signin to update your organization related information",i.style.color="#333333";var o=document.createElement("a");o.href="https://authiqa.com",o.textContent="Visit Authiqa",o.style.color="#3498db",e.appendChild(n),e.appendChild(i),e.appendChild(o),t.appendChild(e);var a=document.createElement("style");a.textContent='\n            .authiqa-error-container {\n                padding: 20px;\n                border: 1px solid #e74c3c;\n                border-radius: 5px;\n                background-color: #fef5f5;\n                text-align: center;\n                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;\n            }\n            .authiqa-error-container h2 {\n                color: #e74c3c;\n                margin-top: 0;\n            }\n            .authiqa-error-container p {\n                color: #333333;\n                margin-bottom: 15px;\n            }\n            .authiqa-error-container a {\n                display: inline-block;\n                margin-top: 15px;\n                color: #3498db;\n                text-decoration: none;\n            }\n            .authiqa-error-container a:hover {\n                text-decoration: underline;\n            }\n        ',document.head.appendChild(a)}},t}();e.AuthiqaWidget=l,window.AuthiqaWidget=l,document.addEventListener("DOMContentLoaded",(function(){try{var t=document.querySelector("script[data-public-key]");if(!t)return void console.error("Script tag with data-public-key not found.");var e=t.getAttribute("data-public-key"),n=t.getAttribute("action");if(!n){var i=window.location.href;n=["signin","signup","verify","reset","update","resend"].find((function(t){return i.includes(t)}))||"signin"}var o=t.getAttribute("termsAndConditions"),a=t.getAttribute("privacy"),r=t.getAttribute("notificationSettings"),s=t.getAttribute("theme")||"light",c="true"===t.getAttribute("disable-styles"),l=t.getAttribute("verifyAuthPath"),d=t.getAttribute("updatePasswordPath"),u=t.getAttribute("resendAuthPath"),h=t.getAttribute("successAuthPath"),p=t.getAttribute("signinAuthPath"),m=void 0,g=t.getAttribute("data-messages");if(g)try{m=JSON.parse(g)}catch(t){console.error("Failed to parse custom messages:",t)}var f=void 0,v=t.getAttribute("data-customization");if(v)try{f=JSON.parse(v)}catch(t){console.error("Failed to parse customization:",t)}if("function"!=typeof window.AuthiqaWidget)return void console.error("AuthiqaWidget not properly registered");var b={publicKey:e||"",container:"authiqa",mode:"popup",theme:s,disableStyles:c,organizationDomain:"authiqa.com",termsAndConditions:o,privacy:a,notificationSettings:r,messages:m,customization:f,verifyAuthPath:l,updatePasswordPath:d,resendAuthPath:u,successAuthPath:h,signinAuthPath:p};new window.AuthiqaWidget(b).show(n)}catch(t){console.error("Error during widget initialization:",t)}}))},752:function(t,e,n){var i=this&&this.__assign||function(){return i=Object.assign||function(t){for(var e,n=1,i=arguments.length;n<i;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},i.apply(this,arguments)},o=this&&this.__awaiter||function(t,e,n,i){return new(n||(n=Promise))((function(o,a){function r(t){try{c(i.next(t))}catch(t){a(t)}}function s(t){try{c(i.throw(t))}catch(t){a(t)}}function c(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(r,s)}c((i=i.apply(t,e||[])).next())}))},a=this&&this.__generator||function(t,e){var n,i,o,a,r={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return a={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(s){return function(c){return function(s){if(n)throw new TypeError("Generator is already executing.");for(;a&&(a=0,s[0]&&(r=0)),r;)try{if(n=1,i&&(o=2&s[0]?i.return:s[0]?i.throw||((o=i.return)&&o.call(i),0):i.next)&&!(o=o.call(i,s[1])).done)return o;switch(i=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return r.label++,{value:s[1],done:!1};case 5:r.label++,i=s[1],s=[0];continue;case 7:s=r.ops.pop(),r.trys.pop();continue;default:if(!((o=(o=r.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){r=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){r.label=s[1];break}if(6===s[0]&&r.label<o[1]){r.label=o[1],o=s;break}if(o&&r.label<o[2]){r.label=o[2],r.ops.push(s);break}o[2]&&r.ops.pop(),r.trys.pop();continue}s=e.call(t,r)}catch(t){s=[6,t],i=0}finally{n=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}};Object.defineProperty(e,"__esModule",{value:!0}),e.ApiService=void 0;var r=n(616),s=n(733),c=function(){function t(t){this.publicKey=t.publicKey,this.config=(0,r.getApiConfig)(t.organizationDomain),(0,s.validateCustomAuthPaths)({verifyAuthPath:t.verifyAuthPath,updatePasswordPath:t.updatePasswordPath,resendAuthPath:t.resendAuthPath,successAuthPath:t.successAuthPath,signinAuthPath:t.signinAuthPath},t.organizationDomain).isValid&&(this.verifyAuthPath=t.verifyAuthPath,this.updatePasswordPath=t.updatePasswordPath,this.resendAuthPath=t.resendAuthPath,this.successAuthPath=t.successAuthPath,this.signinAuthPath=t.signinAuthPath)}return t.prototype.signup=function(t){return o(this,void 0,void 0,(function(){var e;return a(this,(function(n){return e=i(i({},t),{verifyAuthPath:this.verifyAuthPath}),[2,fetch("".concat(this.config.API_BASE,"/auth/signup"),{method:"POST",headers:{"Content-Type":"application/json","X-Public-Key":this.publicKey},body:JSON.stringify(e)})]}))}))},t.prototype.resetPassword=function(t){return o(this,void 0,void 0,(function(){var e;return a(this,(function(n){return e=i(i({},t),{updatePasswordPath:this.updatePasswordPath}),[2,fetch("".concat(this.config.API_BASE,"/auth/reset-password"),{method:"POST",headers:{"Content-Type":"application/json","X-Public-Key":this.publicKey},body:JSON.stringify(e)})]}))}))},t.prototype.resendConfirmation=function(t){return o(this,void 0,void 0,(function(){var e;return a(this,(function(n){return e=i(i({},t),{verifyAuthPath:this.verifyAuthPath}),[2,fetch("".concat(this.config.API_BASE,"/auth/request-new-confirmation"),{method:"POST",headers:{"Content-Type":"application/json","X-Public-Key":this.publicKey},body:JSON.stringify(e)})]}))}))},t.prototype.getApiBase=function(){return this.config.API_BASE},t.prototype.getOrganizationDetails=function(){return o(this,void 0,void 0,(function(){var t,e,n,i,o,r;return a(this,(function(a){switch(a.label){case 0:t="".concat(this.config.API_BASE,"/auth/organization-details"),a.label=1;case 1:return a.trys.push([1,4,,5]),[4,fetch(t,{method:"GET",headers:{"X-Public-Key":this.publicKey,"Content-Type":"application/json"}})];case 2:if(!(e=a.sent()).ok)throw new Error("API Error: ".concat(e.statusText));return o=(i=JSON).parse,[4,e.text()];case 3:if((n=o.apply(i,[a.sent()])).success&&n.data)return[2,n.data];throw new Error("Invalid response format from server");case 4:throw r=a.sent(),console.error("Organization Details Request Failed",r),r;case 5:return[2]}}))}))},t.prototype.checkAuthStatus=function(){return o(this,void 0,void 0,(function(){var t;return a(this,(function(e){switch(e.label){case 0:return[4,fetch("".concat(this.config.API_BASE).concat(this.config.ENDPOINTS.AUTH_STATUS),{headers:{"X-Public-Key":this.publicKey,"Content-Type":"application/json"}})];case 1:if(!(t=e.sent()).ok)throw new Error("API Error: ".concat(t.statusText));return[2,t.json()]}}))}))},t}();e.ApiService=c},616:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.getApiConfig=void 0,e.getApiConfig=function(t){var e;return{API_BASE:"staging"==(((null==(e=document.querySelector("script[data-public-key]"))?void 0:e.getAttribute("src"))||"").includes("staging.widget.authiqa.com")?"staging":"production")?"https://staging.api.authiqa.com":"https://api.".concat(t),ENDPOINTS:{ORGANIZATION_DETAILS:"/auth/organization-details",AUTH_STATUS:"/auth/status"}}}},149:(t,e)=>{function n(){var t;return"staging"==(((null==(t=document.querySelector("script[data-public-key]"))?void 0:t.getAttribute("src"))||"").includes("staging.widget.authiqa.com")?"staging":"production")?"https://staging.api.authiqa.com":"https://api.authiqa.com"}Object.defineProperty(e,"__esModule",{value:!0}),e.STYLE_CONSTANTS=e.THEMES=e.API_ENDPOINTS=void 0,e.API_ENDPOINTS={ORGANIZATION_DETAILS:"".concat(n(),"/auth/organization-details"),AUTH_STATUS:"".concat(n(),"/auth/status")},e.THEMES={light:{background:"#ffffff",text:"#000000",border:"#e0e0e0",modalOverlay:"rgba(0, 0, 0, 0.5)",labelColor:"#333333"},dark:{background:"#1a1a1a",text:"#ffffff",border:"#333333",modalOverlay:"rgba(0, 0, 0, 0.7)",labelColor:"#ffffff"}},e.STYLE_CONSTANTS={STYLE_ELEMENT_ID:"authiqa-styles",CONTAINER_CLASS:"authiqa-container",THEMES:{LIGHT:"light",DARK:"dark"}}},745:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.defaultCustomization=void 0,e.defaultCustomization={layout:{padding:"1.5rem",paddingTop:"1.25rem",margin:"2rem",borderRadius:"16px",maxWidth:"400px"},colors:{background:"#ffffff",buttonBackground:"#000000",buttonText:"#ffffff",inputBackground:"#ffffff",inputText:"#000000",borderColor:"#e5e5e5"},typography:{titleText:{signinText:"Sign In",signupText:"Create Account",resetText:"Reset Password",updateText:"Update Password",verifyText:"Verify Email",resendText:"Resend Confirmation"},subtitleText:{signinText:"",signupText:"",resetText:"",updateText:"",verifyText:"",resendText:""},titleSize:"2rem",titleColor:"#1a1a1a",labelSize:"0.9rem",fontFamily:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',labelColor:"#333333",labelFontWeight:"400",titleAlignment:"center",titleWeight:"600",titleLineHeight:"1.2",termsText:{agreePrefix:"I agree with the",andConnector:"and",defaultPrefix:"default",linkText:{terms:"Terms of Service",privacy:"Privacy Policy",notifications:"Notification Settings"},textColor:"#333333",linkColor:"#000000"},navTextColor:"#1a1a1a",navTextColorDark:"#ffffff"},inputs:{emailPlaceholder:"Email Address",passwordPlaceholder:"Password",usernamePlaceholder:"Username",confirmPasswordPlaceholder:"Confirm Password",emailLabel:"Email",passwordLabel:"Password",usernameLabel:"Username",confirmPasswordLabel:"Confirm Password",borderRadius:"4px",height:"50px",width:"100%",padding:"0 1rem",margin:"0 0 1rem 0",fontSize:"1rem",fontWeight:"400",focusBorderColor:"#000000",focusBoxShadow:"none",placeholderAlign:"left"},buttons:{signinText:"Sign In",signupText:"Create Account",resetText:"Reset Password",updateText:"Update Password",verifyText:"Verify Email",resendText:"Resend Confirmation",height:"40px",width:"100%",borderRadius:"4px",hoverBackground:"#27272a"},navLinks:{signinPrompt:"Already have an account?",signinLinkText:"Sign In",signupPrompt:"Don't have an account?",signupLinkText:"Sign Up",forgotPrompt:"Forgot Password?",forgotLinkText:"Reset",fontSize:"0.95rem",color:"#1a1a1a",fontFamily:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',textAlign:"center",marginTop:"1.5rem",marginBottom:"0",fontWeight:"400",linkColor:"#0070f3",linkFontWeight:"500",backToSigninPrompt:"Back to Sign In?"}}},113:function(t,e,n){var i=this&&this.__assign||function(){return i=Object.assign||function(t){for(var e,n=1,i=arguments.length;n<i;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},i.apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.StyleGenerator=void 0;var o=n(745),a=function(){function t(t){var e,n;this.customization=i(i(i({},o.defaultCustomization),t),{typography:i(i(i({},o.defaultCustomization.typography),null==t?void 0:t.typography),{titleText:i(i({},o.defaultCustomization.typography.titleText),null===(e=null==t?void 0:t.typography)||void 0===e?void 0:e.titleText),subtitleText:i(i({},o.defaultCustomization.typography.subtitleText),null===(n=null==t?void 0:t.typography)||void 0===n?void 0:n.subtitleText)})})}return t.prototype.generateStyles=function(){var t,e,n,i,o,a,r,s,c,l,d,u,h,p,m,g,f,v,b,y,w,x,A,k,C,S,q,P,T,L,E,z,M,I,_,O,U,N=this.customization,j=N.layout,R=N.colors,B=N.typography,D=N.buttons,F=R.background&&("#27272a"===R.background.toLowerCase()||"#18181b"===R.background.toLowerCase());return"\n        .authiqa-container {\n            background-color: ".concat(R.background,";\n            padding: ").concat(j.paddingTop||"1.25rem"," ").concat(j.padding," ").concat(j.padding," ").concat(j.padding,";\n            margin: ").concat(j.margin,";\n            border-radius: ").concat(j.borderRadius,";\n            max-width: ").concat(j.maxWidth,";\n            font-family: ").concat(B.fontFamily,";\n            --authiqa-nav-text-color: ").concat(B.navTextColor||"#1a1a1a",";\n            --authiqa-nav-text-color-dark: ").concat(B.navTextColorDark||"#ffffff",";\n        }\n\n        .authiqa-container h1 {\n            color: ").concat(B.titleColor,";\n            font-size: ").concat(B.titleSize,";\n            margin-top: 0;\n            margin-bottom: 2rem;\n            text-align: ").concat(B.titleAlignment||"center",";\n            font-weight: ").concat(B.titleWeight||"600",";\n            line-height: ").concat(B.titleLineHeight||"1.2",";\n        }\n\n        .authiqa-container input {\n            background-color: ").concat(R.inputBackground,";\n            color: ").concat(R.inputText,";\n            border: 1px solid ").concat(R.borderColor,";\n            border-radius: ").concat((null===(t=this.customization.inputs)||void 0===t?void 0:t.borderRadius)||"4px",";\n            height: ").concat((null===(e=this.customization.inputs)||void 0===e?void 0:e.height)||"50px",";\n            width: ").concat((null===(n=this.customization.inputs)||void 0===n?void 0:n.width)||"100%",";\n            padding: ").concat((null===(i=this.customization.inputs)||void 0===i?void 0:i.padding)||"0 1rem",";\n            margin: ").concat((null===(o=this.customization.inputs)||void 0===o?void 0:o.margin)||"0 0 1rem 0",";\n            font-size: ").concat((null===(a=this.customization.inputs)||void 0===a?void 0:a.fontSize)||"1rem",";\n            font-weight: ").concat((null===(r=this.customization.inputs)||void 0===r?void 0:r.fontWeight)||"400",";\n            box-sizing: border-box;\n            transition: border-color 0.2s ease, box-shadow 0.2s ease;\n        }\n        .authiqa-container input:focus {\n            border-color: ").concat((null===(s=this.customization.inputs)||void 0===s?void 0:s.focusBorderColor)||"#000000",";\n            box-shadow: ").concat((null===(c=this.customization.inputs)||void 0===c?void 0:c.focusBoxShadow)||"none",';\n            outline: none;\n        }\n\n        /* Make button selectors more specific to override defaults */\n        .authiqa-container button,\n        .authiqa-container button[type="submit"],\n        .authiqa-container .authiqa-button {\n            background-color: ').concat(R.buttonBackground," !important;\n            color: ").concat(R.buttonText," !important;\n            height: ").concat(D.height||"40px"," !important;\n            width: ").concat(D.width||"100%"," !important;\n            border-radius: ").concat(D.borderRadius,' !important;\n            display: flex !important;\n            align-items: center !important;\n            justify-content: center !important;\n            line-height: 1 !important;\n        }\n        .authiqa-container button[type="submit"]:hover,\n        .authiqa-container .authiqa-button:hover {\n            background-color: ').concat(D.hoverBackground||R.buttonBackground," !important;\n        }\n        \n        /* Label styling */\n        .authiqa-container .authiqa-label,\n        .authiqa-label {\n            display: block !important;\n            margin-bottom: 0.5rem !important;\n            padding-left: 0.09rem !important;\n            font-weight: ").concat(B.labelFontWeight||"400"," !important;\n            color: ").concat(B.labelColor||(F?"#ffffff":R.inputText||"#333333")," !important;\n            font-size: ").concat(B.labelSize||"0.9rem",' !important;\n            height: auto !important;\n            line-height: 1.2 !important;\n        }\n        \n        /* Dark theme specific styles */\n        .authiqa-container[data-theme="dark"] .authiqa-label,\n        .authiqa-container[data-theme="dark"] label {\n            color: #ffffff !important;\n        }\n\n        .authiqa-container .authiqa-labeled-input {\n            margin-bottom: 1rem !important; /* Decreased from 1.5rem to 1.2rem (about 5px less) */\n        }\n\n        /* Ensure password container properly styles the label */\n        .authiqa-container .authiqa-password-container .authiqa-label {\n            display: block !important;\n            margin-bottom: 0.3rem !important; /* Changed to 0.3rem (approximately 5px) */\n            padding-left: 0.08rem !important; /* Added left padding to move labels slightly right */\n            font-weight: 500 !important;\n            height: 14px !important; /* Added fixed height */\n            line-height: 14px !important; /* Added line height to match height */\n        }\n\n        /* Password field container */\n        .authiqa-container .authiqa-password-container {\n            position: relative !important;\n            width: 100% !important;\n        }\n\n        /* Password toggle button */\n        .authiqa-container .password-toggle {\n            position: absolute !important;\n            right: 12px !important;\n            top: 50% !important;\n            transform: translateY(-50%) !important;\n            background: none !important;\n            border: none !important;\n            color: ').concat(R.inputText?R.inputText+"99":"#a1a1aa",' !important;\n            cursor: pointer !important;\n            padding: 0 !important;\n            margin: 0 !important;\n            display: flex !important;\n            align-items: center !important;\n            justify-content: center !important;\n            height: 100% !important;\n            width: 40px !important;\n            z-index: 2 !important;\n        }\n        \n        /* Terms container - adjusted spacing */\n        .authiqa-container .terms-container {\n            display: flex !important;\n            align-items: flex-start !important;\n            margin: 0.5rem 0 1rem 0 !important; /* Decreased bottom margin from 3rem to 1rem */\n            position: relative !important;\n        }\n\n        .authiqa-container .terms-container input[type="checkbox"] {\n            margin: 0.25rem 0.5rem 0 0 !important; /* Standardized margins */\n            position: static !important; /* Remove relative positioning */\n        }\n\n        .authiqa-container .terms-container label {\n            color: ').concat((null===(l=B.termsText)||void 0===l?void 0:l.textColor)||R.inputText||"#333333"," !important;\n            font-size: 0.875rem !important;\n            line-height: 1.4 !important;\n            margin: 0 !important;\n            padding-top: 0 !important;\n            margin-left: 0 !important;\n            flex: 1 !important;\n        }\n        \n        .authiqa-container .terms-container a {\n            color: ").concat((null===(d=B.termsText)||void 0===d?void 0:d.linkColor)||R.buttonBackground||"#000000",' !important;\n            text-decoration: none !important;\n        }\n        \n        /* Input field styling - highest priority for user customization */\n        .authiqa-container input[type="text"],\n        .authiqa-container input[type="email"],\n        .authiqa-container input[type="password"],\n        .authiqa-input {\n            width: ').concat((null===(u=this.customization.inputs)||void 0===u?void 0:u.width)||"100%"," !important;\n            height: ").concat((null===(h=this.customization.inputs)||void 0===h?void 0:h.height)||"50px"," !important;\n            padding: ").concat((null===(p=this.customization.inputs)||void 0===p?void 0:p.padding)||"0 1rem"," !important;\n            font-size: ").concat((null===(m=this.customization.inputs)||void 0===m?void 0:m.fontSize)||"1rem"," !important;\n            font-weight: ").concat((null===(g=this.customization.inputs)||void 0===g?void 0:g.fontWeight)||"400"," !important;\n            border-radius: ").concat((null===(f=this.customization.inputs)||void 0===f?void 0:f.borderRadius)||"4px"," !important;\n            background-color: ").concat(R.inputBackground," !important;\n            color: ").concat(R.inputText," !important;\n            border: 1px solid ").concat(R.borderColor," !important;\n            margin: ").concat((null===(v=this.customization.inputs)||void 0===v?void 0:v.margin)||"0 0 1rem 0",' !important;\n            box-sizing: border-box !important;\n            transition: border-color 0.2s ease, box-shadow 0.2s ease !important;\n        }\n        .authiqa-container input[type="text"]:focus,\n        .authiqa-container input[type="email"]:focus,\n        .authiqa-container input[type="password"]:focus,\n        .authiqa-input:focus {\n            border-color: ').concat((null===(b=this.customization.inputs)||void 0===b?void 0:b.focusBorderColor)||"#000000"," !important;\n            box-shadow: ").concat((null===(y=this.customization.inputs)||void 0===y?void 0:y.focusBoxShadow)||"none",' !important;\n            outline: none !important;\n        }\n\n        /* Checkbox specific styling */\n        .authiqa-container input[type="checkbox"] {\n            width: auto !important;\n            height: auto !important;\n            margin-right: 8px !important;\n            margin-top: 3px !important;\n            background-color: transparent !important;\n        }\n\n        /* Decrease spacing between password field and terms */\n        .authiqa-container .authiqa-labeled-input {\n            margin-bottom: 0.5rem !important; /* Decreased from 1rem to 0.5rem */\n        }\n\n        /* Button spacing - no need to change as the terms container\'s bottom margin will create space */\n        .authiqa-container form button[type="submit"] {\n            margin-top: 0 !important; /* Remove top margin as we\'re using bottom margin on terms container */\n        }\n\n        .authiqa-container input[type="text"]::placeholder,\n        .authiqa-container input[type="email"]::placeholder,\n        .authiqa-container input[type="password"]::placeholder,\n        .authiqa-input::placeholder {\n            color: ').concat(R.inputPlaceholder||"#a3a3a3"," !important;\n            text-align: ").concat((null===(w=this.customization.inputs)||void 0===w?void 0:w.placeholderAlign)||"left"," !important;\n        }\n\n        /* Navigation (alternate-action) styling */\n        .authiqa-container .alternate-action {\n            text-align: ").concat((null===(x=this.customization.navLinks)||void 0===x?void 0:x.textAlign)||"center"," !important;\n            margin-top: ").concat((null===(A=this.customization.navLinks)||void 0===A?void 0:A.marginTop)||"1.5rem"," !important;\n            margin-bottom: ").concat((null===(k=this.customization.navLinks)||void 0===k?void 0:k.marginBottom)||"0"," !important;\n            font-size: ").concat((null===(C=this.customization.navLinks)||void 0===C?void 0:C.fontSize)||"0.95rem"," !important;\n            color: ").concat((null===(S=this.customization.navLinks)||void 0===S?void 0:S.color)||"var(--authiqa-nav-text-color, #1a1a1a)"," !important;\n            font-family: ").concat((null===(q=this.customization.navLinks)||void 0===q?void 0:q.fontFamily)||B.fontFamily," !important;\n            font-weight: ").concat((null===(P=this.customization.navLinks)||void 0===P?void 0:P.fontWeight)||"400",' !important;\n        }\n        .authiqa-container[data-theme="dark"] .alternate-action {\n            color: var(--authiqa-nav-text-color-dark, #ffffff) !important;\n        }\n        .authiqa-container .alternate-action a {\n            color: ').concat((null===(T=this.customization.navLinks)||void 0===T?void 0:T.linkColor)||"#0070f3"," !important;\n            font-weight: ").concat((null===(L=this.customization.navLinks)||void 0===L?void 0:L.linkFontWeight)||"500"," !important;\n            text-decoration: none !important;\n        }\n        .authiqa-container .alternate-action a:hover {\n            text-decoration: underline !important;\n        }\n        /* Forgot password link styling */\n        .authiqa-container .forgot-password {\n            text-align: ").concat((null===(E=this.customization.navLinks)||void 0===E?void 0:E.textAlign)||"right"," !important;\n            margin-top: -1rem !important;\n            margin-bottom: 1rem !important;\n            font-size: ").concat((null===(z=this.customization.navLinks)||void 0===z?void 0:z.fontSize)||"0.95rem"," !important;\n            color: ").concat((null===(M=this.customization.navLinks)||void 0===M?void 0:M.color)||"#525252"," !important;\n            font-family: ").concat((null===(I=this.customization.navLinks)||void 0===I?void 0:I.fontFamily)||B.fontFamily," !important;\n            font-weight: ").concat((null===(_=this.customization.navLinks)||void 0===_?void 0:_.fontWeight)||"400"," !important;\n        }\n        .authiqa-container .forgot-password a {\n            color: ").concat((null===(O=this.customization.navLinks)||void 0===O?void 0:O.linkColor)||"#0070f3"," !important;\n            font-weight: ").concat((null===(U=this.customization.navLinks)||void 0===U?void 0:U.linkFontWeight)||"500"," !important;\n            text-decoration: none !important;\n        }\n        .authiqa-container .forgot-password a:hover {\n            text-decoration: underline !important;\n        }\n    ")},t}();e.StyleGenerator=a},92:(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.generateTermsContainerStyles=e.generatePageLayoutStyles=e.getComponentStyles=e.getStyleContent=void 0;var i=n(149);e.getStyleContent=function(t){return"\n        /* Dynamically generated styles for ".concat(t," theme */\n        :root {\n            --authiqa-bg-color: ").concat("dark"===t?"#18181b":"#ffffff",";\n            --authiqa-text-color: ").concat("dark"===t?"#ffffff":"#1a1a1a",";\n            --authiqa-border-color: ").concat("dark"===t?"#3f3f46":"#e5e5e5",";\n            --authiqa-input-bg: ").concat("dark"===t?"#27272a":"#ffffff",";\n            --authiqa-button-bg: ").concat("dark"===t?"#ffffff":"#18181b",";\n            --authiqa-button-text: ").concat("dark"===t?"#18181b":"#ffffff",";\n        }\n    ")},e.getComponentStyles=function(t){void 0===t&&(t="light");var e=i.THEMES[t];return{modal:{overlay:{position:"fixed",top:0,left:0,width:"100%",height:"100%",backgroundColor:e.modalOverlay,zIndex:1e3},container:{position:"relative",width:"500px",margin:"50px auto",backgroundColor:e.background,color:e.text,borderRadius:"8px",padding:"20px",border:"1px solid ".concat(e.border)}},iframe:{border:"none",width:"100%",height:"600px",backgroundColor:e.background},message:{position:"fixed",top:"20px",left:"50%",transform:"translateX(-50%)",padding:"12px 24px",borderRadius:"4px",fontSize:"14px",fontWeight:"500",opacity:"0",transition:"opacity 0.3s ease"},messageSuccess:{backgroundColor:"#4CAF50",color:"white"},messageError:{backgroundColor:"#f44336",color:"white"},messageShow:{opacity:"1"}}},e.generatePageLayoutStyles=function(t){var e,n=null===(e=t.customization)||void 0===e?void 0:e.pageLayout;if(!n)return"";var i="\n        body {\n            ".concat(n.backgroundColor?"background-color: ".concat(n.backgroundColor,";"):"","\n            margin: 0;\n            min-height: 100vh;\n            display: flex;\n            align-items: ").concat(function(t){switch(t){case"top":return"flex-start";case"bottom":return"flex-end";default:return"center"}}(n.formPosition),";\n            justify-content: ").concat(function(t){switch(t){case"left":return"flex-start";case"right":return"flex-end";default:return"center"}}(n.formPosition),";\n        }\n    ");return n&&(i+="\n        #".concat(t.container," {\n            ").concat(n.formMarginTop?"margin-top: ".concat(n.formMarginTop,";"):"","\n            ").concat(n.formMarginBottom?"margin-bottom: ".concat(n.formMarginBottom,";"):"","\n            ").concat(n.formMarginLeft?"margin-left: ".concat(n.formMarginLeft,";"):"","\n            ").concat(n.formMarginRight?"margin-right: ".concat(n.formMarginRight,";"):"","\n        }\n        ")),i},e.generateTermsContainerStyles=function(t){var e;if(!(null===(e=t.customization)||void 0===e?void 0:e.colors))return"";var n=t.customization.colors;return'\n        /* Terms container styling */\n        .authiqa-container .terms-container {\n            display: flex;\n            align-items: flex-start;\n            margin: 0.75rem 0;\n        }\n        \n        .authiqa-container .terms-container input[type="checkbox"] {\n            margin-top: 3px;\n            margin-right: 8px;\n        }\n        \n        .authiqa-container .terms-container label {\n            color: '.concat(n.inputText||"#333333",";\n            font-size: 0.875rem;\n            line-height: 1.4;\n            margin: 0;\n            flex: 1;\n        }\n        \n        .authiqa-container .terms-container a {\n            color: ").concat(n.buttonBackground||"#000000",";\n            text-decoration: none;\n        }\n        \n        .authiqa-container .terms-container a:hover {\n            text-decoration: underline;\n        }\n    ")}},733:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.validateCustomAuthPaths=e.validateAuthUrls=void 0,e.validateAuthUrls=function(t,e){if(!t)return{isValid:!1,message:"Authentication URLs are required"};for(var n=0,i=["signup","signin","verify","reset","update","resend","successful"];n<i.length;n++){var o=i[n];if(!t[o])return{isValid:!1,message:"".concat(o," is required")};try{if("https:"!==new URL(t[o]).protocol)return{isValid:!1,message:"".concat(o," must use HTTPS protocol")}}catch(t){return{isValid:!1,message:"Invalid URL format for ".concat(o)}}}return{isValid:!0,message:""}},e.validateCustomAuthPaths=function(t,e){for(var n=0,i=Object.entries(t);n<i.length;n++){var o=i[n],a=o[0],r=o[1];if(r)try{if("https:"!==new URL(r).protocol)return{isValid:!1,message:"".concat(a," must use HTTPS protocol")}}catch(t){return{isValid:!1,message:"".concat(a," must be a complete URL (e.g., https://domain.com/path)")}}}return{isValid:!0,message:""}}}},e={};function n(i){var o=e[i];if(void 0!==o)return o.exports;var a=e[i]={id:i,exports:{}};return t[i].call(a.exports,a,a.exports,n),a.exports}return n.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return n.d(e,{a:e}),e},n.d=(t,e)=>{for(var i in e)n.o(e,i)&&!n.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),n.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.nc=void 0,n(156)})()));