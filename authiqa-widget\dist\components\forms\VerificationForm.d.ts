/**
 * Email Verification Form Component
 */
import { WidgetConfig, AuthUrls } from '../../lib/types';
export interface VerificationFormOptions {
    config: WidgetConfig;
    authUrls: AuthUrls | null;
    apiBase: string;
    container: HTMLElement;
}
export declare class VerificationForm {
    private options;
    constructor(options: VerificationFormOptions);
    /**
     * Renders the verification status and handles email verification
     */
    render(): void;
    /**
     * Handles email verification process
     */
    private handleEmailVerification;
    /**
     * Shows verification success message
     */
    private showVerificationSuccess;
    /**
     * Shows verification error message
     */
    private showVerificationError;
}
