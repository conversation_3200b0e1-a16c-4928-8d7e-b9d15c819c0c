/**
 * Sign In Form Component
 */
import { WidgetConfig, AuthUrls } from '../../lib/types';
import { GoogleSSOConfig } from '../../lib/google-auth/GoogleSSOManager';
export interface SignInFormOptions {
    config: WidgetConfig;
    authUrls: AuthUrls | null;
    apiBase: string;
    googleSsoConfig?: GoogleSSOConfig;
    container: HTMLElement;
}
export declare class SignInForm {
    private options;
    constructor(options: SignInFormOptions);
    /**
     * Renders the sign-in form
     */
    render(): void;
    /**
     * Handles form submission
     */
    private handleSubmit;
    /**
     * Handles successful signin response
     */
    private handleSuccessfulSignin;
    /**
     * Adds Google SSO button if enabled
     */
    private addGoogleSSO;
    /**
     * Adds navigation link to signup
     */
    private addNavigationLink;
}
