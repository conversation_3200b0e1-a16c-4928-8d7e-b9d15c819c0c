/**
 * Sign Up Form Component
 */
import { WidgetConfig, AuthUrls } from '../../lib/types';
import { GoogleSSOConfig } from '../../lib/google-auth/GoogleSSOManager';
export interface SignUpFormOptions {
    config: WidgetConfig;
    authUrls: AuthUrls | null;
    apiBase: string;
    googleSsoConfig?: GoogleSSOConfig;
    container: HTMLElement;
}
export declare class SignUpForm {
    private options;
    constructor(options: SignUpFormOptions);
    /**
     * Renders the sign-up form
     */
    render(): void;
    /**
     * Creates terms and privacy section
     */
    private createTermsAndPrivacySection;
    /**
     * Handles form submission
     */
    private handleSubmit;
    /**
     * Handles successful signup response
     */
    private handleSuccessfulSignup;
    /**
     * Adds Google SSO button if enabled
     */
    private addGoogleSSO;
    /**
     * Adds navigation link to signin
     */
    private addNavigationLink;
}
