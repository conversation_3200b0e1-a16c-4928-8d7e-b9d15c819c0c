/**
 * Reset Password Form Component
 */
import { WidgetConfig, AuthUrls } from '../../lib/types';
export interface ResetPasswordFormOptions {
    config: WidgetConfig;
    authUrls: AuthUrls | null;
    apiBase: string;
    container: HTMLElement;
}
export declare class ResetPasswordForm {
    private options;
    constructor(options: ResetPasswordFormOptions);
    /**
     * Renders the reset password form
     */
    render(): void;
    /**
     * Handles form submission
     */
    private handleSubmit;
    /**
     * Handles successful reset response
     */
    private handleSuccessfulReset;
    /**
     * Adds navigation link back to signin
     */
    private addNavigationLink;
}
