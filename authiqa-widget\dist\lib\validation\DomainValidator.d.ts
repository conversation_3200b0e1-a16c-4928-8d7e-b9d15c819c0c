/**
 * Domain validation and authorization utilities
 */
export declare class DomainValidator {
    /**
     * Validates if the current domain is authorized
     */
    static validateDomain(organizationUrl: string): boolean;
    /**
     * Checks if the widget is running in development mode
     */
    static isDevelopmentMode(): boolean;
    /**
     * Shows unauthorized domain error to user
     */
    static showUnauthorizedError(containerId: string): void;
}
