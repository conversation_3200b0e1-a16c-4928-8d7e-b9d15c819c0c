import { AuthUrls, WidgetConfig } from './lib/types';
import { WidgetCustomization } from './lib/customization-types';
import './styles/default-theme.css';
declare global {
    interface Window {
        AuthiqaGlobalConfig?: {
            customization?: Partial<WidgetCustomization>;
            messages?: Record<string, string>;
        };
        _authiqaGoogleOneTapDismissed?: boolean;
        _authiqaGoogleOneTapPromptActive?: boolean;
    }
}
export declare class AuthiqaWidget {
    private config;
    private authUrls;
    private api;
    private currentAction;
    private googleSsoConfig?;
    constructor(config: WidgetConfig);
    getAuthUrls(): AuthUrls;
    initialize(): Promise<void>;
    show(action: keyof AuthUrls): void;
    private initializeContainer;
    private createLabeledInput;
    private createPasswordField;
    private renderSignInForm;
    private renderSignUpForm;
    private validatePassword;
    private renderResetPasswordForm;
    private renderUpdatePasswordForm;
    private renderResendConfirmationForm;
    private renderVerificationStatus;
    private handleEmailVerification;
    private showMessage;
    private getCustomSuccessMessage;
    private setLoadingState;
    private getCustomLoadingMessage;
    private injectStyles;
    private generateCustomStyles;
    private updateTheme;
    cleanup(): void;
    private handleApiError;
    private validateDomain;
    private isDevelopmentMode;
    private showUnauthorizedError;
}
