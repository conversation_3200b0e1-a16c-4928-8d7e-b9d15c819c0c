/* Base styles */
body {
    margin: 0;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

body[data-theme="dark"] {
    background-color: #18181b;
}

.authiqa-container {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    max-width: 400px;
    width: 100%;
    margin: 2rem;
    padding: 1.25rem 1.5rem 1.5rem 1.5rem; /* Reduced horizontal padding from 2.5rem to 1.5rem */
    border-radius: 16px;
}

.authiqa-container h1 {
    font-size: 2rem;
    font-weight: 600;
    margin-top: 0; /* Added to ensure no extra top margin */
    margin-bottom: 2rem;
    color: #1a1a1a;
    text-align: center;
}

.authiqa-container form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.authiqa-container label,
.authiqa-label {
    font-size: 1rem;
    font-weight: 400;
    color: #1a1a1a;
    margin-bottom: 0.5rem !important; /* Changed to 0.3rem (approximately 5px) */
    padding-left: 0.09rem !important; /* Added left padding to move labels slightly right */
    display: block;
    height: 14px !important; /* Added fixed height */
    line-height: 14px !important; /* Added line height to match height */
}

/* Add more spacing between input groups */
.authiqa-container .authiqa-labeled-input {
    margin-bottom: 1rem !important; /* Decreased from 1.5rem to 1rem (about 16px) */
}

.authiqa-container input[type="text"],
.authiqa-container input[type="email"],
.authiqa-container input[type="password"] {
    width: 100%;
    height: 50px; /* Set height to 64px (14x64) */
    padding: 0 1rem;
    font-size: 1rem;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    background-color: #ffffff;
    transition: border-color 0.2s ease;
    box-sizing: border-box;
}

.authiqa-container input[type="text"]:focus,
.authiqa-container input[type="email"]:focus,
.authiqa-container input[type="password"]:focus {
    outline: none;
    border-color: #000000;
}

.authiqa-container input[type="text"]::placeholder,
.authiqa-container input[type="email"]::placeholder,
.authiqa-container input[type="password"]::placeholder {
    color: #a3a3a3;
}

.authiqa-container .terms-container {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.authiqa-container input[type="checkbox"] {
    margin-top: 0.25rem; /* Adjusted to align with text */
    margin-right: 0.5rem; /* Standardized margin */
    margin-left: 0; /* Reset left margin */
    position: static; /* Remove relative positioning */
    top: auto; /* Remove top offset */
}

.authiqa-container .terms-container label {
    font-size: 0.875rem;
    color: #525252;
    line-height: 1.4;
    margin-top: 0; /* Ensure no top margin */
    padding-top: 0; /* Ensure no top padding */
}

.authiqa-container .terms-container a {
    color: #000000;
    text-decoration: none;
}

.authiqa-container .terms-container a:hover {
    text-decoration: underline;
}

.authiqa-container .forgot-password,
.authiqa-container .alternate-action {
    color: #ffffff !important;
    font-size: 0.95rem !important;
    text-align: left !important;
}
.authiqa-container .alternate-action {
    text-align: center;
}
.authiqa-container .forgot-password a,
.authiqa-container .alternate-action a {
    color: #10D5C6 !important;
    text-decoration: underline;
    font-weight: 500;
    margin-left: 0.25rem;
    transition: color 0.2s;
}
.authiqa-container .forgot-password a:hover,
.authiqa-container .alternate-action a:hover {
    color: #0ea5e9 !important;
}

/* Update the button styles to include proper centering */
.authiqa-container button[type="submit"] {
    width: 100%;
    height: 40px; /* Changed to fixed 40px height (approximately 14px) */
    padding: 0 1rem;
    font-size: 1rem;
    font-weight: 500;
    color: #ffffff;
    background-color: #18181b;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    margin-top: 0.5rem; /* Decreased from 1rem to 0.5rem (about 8px) */
}

.authiqa-container button[type="submit"]:hover {
    background-color: #27272a;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Add this new style for the active/clicked state */
.authiqa-container button[type="submit"]:active {
    transform: scale(0.98);
    box-shadow: none;
    background-color: #000000;
}

.authiqa-container button[type="submit"]:disabled {
    background-color: #71717a;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Loading state styles */
.authiqa-container button[type="submit"].loading {
    position: relative;
    color: transparent !important;
}

.authiqa-container button[type="submit"].loading::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    top: 50%;
    left: 50%;
    margin: -10px 0 0 -10px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-left-color: transparent;
    animation: button-loading-spinner 1s linear infinite;
}

/* Loading text container */
.authiqa-container .loading-text {
    text-align: center;
    margin-top: 8px;
    font-size: 0.875rem;
    color: #525252;
}

/* Dark theme loading text */
.authiqa-container[data-theme="dark"] .loading-text {
    color: #a1a1aa;
}

@keyframes button-loading-spinner {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Dark theme button styles */
.authiqa-container[data-theme="dark"] button[type="submit"] {
    background-color: #ffffff;
    color: #18181b;
}

.authiqa-container[data-theme="dark"] button[type="submit"]:hover {
    background-color: #e5e5e5;
}

.authiqa-container[data-theme="dark"] button[type="submit"]:disabled {
    background-color: #a1a1aa;
    color: #18181b;
}

.authiqa-container[data-theme="dark"] button[type="submit"].loading::after {
    border-color: #18181b;
    border-left-color: transparent;
}

/* Center alternate-action and set color for both themes */
.authiqa-container .alternate-action {
    text-align: center !important;
    margin-top: 1.5rem;
    font-size: 0.95rem;
    color: #1a1a1a !important; /* Force dark text in light theme */
}

.authiqa-container .alternate-action a {
    color: #10D5C6 !important;
    text-decoration: underline;
    font-weight: 500;
    margin-left: 0.25rem;
    transition: color 0.2s;
}

.authiqa-container .alternate-action a:hover {
    color: #0ea5e9 !important;
}

/* Dark theme overrides */
.authiqa-container[data-theme="dark"] .alternate-action {
    color: #ffffff !important;
}

/* Dark theme */
.authiqa-container[data-theme="dark"] {
    color: #ffffff;
    background-color: #27272a;
}

.authiqa-container[data-theme="dark"] h1 {
    color: #ffffff;
}

.authiqa-container[data-theme="dark"] label,
.authiqa-container[data-theme="dark"] .authiqa-label {
    color: #ffffff !important; /* Ensure white label text in dark theme */
    
}

.authiqa-container[data-theme="dark"] input[type="text"],
.authiqa-container[data-theme="dark"] input[type="email"],
.authiqa-container[data-theme="dark"] input[type="password"] {
    background-color: #18181b;
    border-color: #3f3f46;
    color: #ffffff;
}

.authiqa-container[data-theme="dark"] input[type="text"]:focus,
.authiqa-container[data-theme="dark"] input[type="email"]:focus,
.authiqa-container[data-theme="dark"] input[type="password"]:focus {
    border-color: #ffffff;
}

.authiqa-container[data-theme="dark"] input[type="text"]::placeholder,
.authiqa-container[data-theme="dark"] input[type="email"]::placeholder,
.authiqa-container[data-theme="dark"] input[type="password"]::placeholder {
    color: #71717a;
}

.authiqa-container[data-theme="dark"] .terms-container label {
    color: #a1a1aa;
}

.authiqa-container[data-theme="dark"] .terms-container a,
.authiqa-container[data-theme="dark"] .forgot-password a {
    color: #ffffff;
}

.authiqa-container[data-theme="dark"] .alternate-action {
    color: #a1a1aa;
}

.authiqa-container[data-theme="dark"] .alternate-action a {
    color: #ffffff;
}

.authiqa-container .password-field-container {
    position: relative;
    width: 100%;
}

.authiqa-container .password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    border: none;
    background: none;
    cursor: pointer;
    padding: 8px;
    color: #71717a;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: color 0.2s ease;
}

.authiqa-container .password-toggle:hover {
    color: #000000;
}

.authiqa-container[data-theme="dark"] .password-toggle {
    color: #a1a1aa;
}

.authiqa-container[data-theme="dark"] .password-toggle:hover {
    color: #ffffff;
}

/* Message styles */
.authiqa-message {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    padding: 1rem 2rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    animation: slideDown 0.3s ease-out;
    max-width: 90%;
    width: auto;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s;
}

.authiqa-message.show {
    opacity: 1;
    pointer-events: auto;
}

.authiqa-message.success {
    background-color: #dcfce7;
    color: #15803d;
    border-left: 4px solid #22c55e;
}

.authiqa-message.error {
    background-color: #fee2e2;
    color: #b91c1c;
    border-left: 4px solid #ef4444;
}

@keyframes slideDown {
    from {
        transform: translate(-50%, -100%);
        opacity: 0;
    }
    to {
        transform: translate(-50%, 0);
        opacity: 1;
    }
}

/* Dark theme message styles */
body[data-theme="dark"] .authiqa-message.success {
    background-color: #064e3b;
    color: #ffffff;
    border-left-color: #059669;
}

body[data-theme="dark"] .authiqa-message.error {
    background-color: #7f1d1d;
    color: #ffffff;
    border-left-color: #dc2626;
}

/* Password validation styling */
.authiqa-container .password-validation-container {
    display: grid;
    grid-template-columns: 1fr 1fr; /* Two columns instead of flex-wrap */
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.authiqa-container .validation-item {
    display: flex;
    align-items: center;
    font-size: 0.75rem;
    color: #a1a1aa;
}

.authiqa-container .validation-dot {
    margin-right: 0.25rem;
    color: #a1a1aa;
}

.authiqa-container .validation-item.valid .validation-dot,
.authiqa-container .validation-item.valid .validation-text {
    color: #10D5C6;
}

/* Dark theme adjustments */
.authiqa-container[data-theme="dark"] .validation-item {
    color: #a1a1aa;
}

.authiqa-container[data-theme="dark"] .validation-item.valid .validation-dot,
.authiqa-container[data-theme="dark"] .validation-item.valid .validation-text {
    color: #10D5C6;
}

/* Google SSO button styles */
#google-signin-btn, #google-signup-btn {
    cursor: pointer;
    transition: background 0.2s, box-shadow 0.2s, transform 0.1s;
}
#google-signin-btn:hover, #google-signup-btn:hover {
    background: #f7f7f7 !important;
    box-shadow: 0 2px 8px rgba(60,64,67,.15);
}
#google-signin-btn:active, #google-signup-btn:active {
    transform: scale(0.98);
    box-shadow: 0 1px 2px rgba(60,64,67,.08);
}

/* Google button container styles to ensure full width */
#google-button-container, #google-signup-button-container {
    width: 100% !important;
    display: block !important;
}

/* Override Google's default button styling to match our widget width */
#google-button-container > div, #google-signup-button-container > div {
    width: 100% !important;
    display: block !important;
}

#google-button-container iframe, #google-signup-button-container iframe {
    width: 100% !important;
    min-width: 100% !important;
    max-width: 100% !important;
    display: block !important;
}

/* More aggressive targeting for Google button elements */
#google-button-container * {
    width: 100% !important;
    max-width: 100% !important;
}

#google-signup-button-container * {
    width: 100% !important;
    max-width: 100% !important;
}
