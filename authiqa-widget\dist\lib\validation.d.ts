import { AuthUrls } from './types';
interface ValidationResult {
    isValid: boolean;
    message: string;
}
export declare function validateAuthUrls(authUrls: AuthUrls, organizationUrl: string): ValidationResult;
export declare function validateCustomAuthPaths(paths: {
    verifyAuthPath?: string;
    updatePasswordPath?: string;
    resendAuthPath?: string;
    successAuthPath?: string;
    signinAuthPath?: string;
}, organizationDomain: string): ValidationResult;
export {};
