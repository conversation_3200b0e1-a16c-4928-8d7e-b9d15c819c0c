/**
 * Form element creation utilities
 */
export declare class FormElements {
    /**
     * Creates a labeled input field with consistent styling
     */
    static createLabeledInput(type: string, id: string, placeholder: string, labelText: string, required?: boolean): {
        container: HTMLDivElement;
        input: HTMLInputElement;
    };
    /**
     * Creates a password field with toggle visibility and validation
     */
    static createPasswordField(placeholder: string, id: string, label?: string): {
        container: HTMLDivElement;
        input: HTMLInputElement;
    };
}
