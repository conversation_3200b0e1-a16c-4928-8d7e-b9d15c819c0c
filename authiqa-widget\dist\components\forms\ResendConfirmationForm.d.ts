/**
 * Resend Confirmation Form Component
 */
import { WidgetConfig, AuthUrls } from '../../lib/types';
export interface ResendConfirmationFormOptions {
    config: WidgetConfig;
    authUrls: AuthUrls | null;
    apiBase: string;
    container: HTMLElement;
}
export declare class ResendConfirmationForm {
    private options;
    constructor(options: ResendConfirmationFormOptions);
    /**
     * Renders the resend confirmation form
     */
    render(): void;
    /**
     * Handles form submission
     */
    private handleSubmit;
    /**
     * Handles successful resend response
     */
    private handleSuccessfulResend;
    /**
     * Adds navigation link back to signin
     */
    private addNavigationLink;
}
