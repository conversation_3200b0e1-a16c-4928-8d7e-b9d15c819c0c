import { AuthResponse, WidgetConfig, OrganizationDetailsResponse } from './types';
export declare class ApiService {
    private publicKey;
    private config;
    private verifyAuthPath?;
    private updatePasswordPath?;
    private resendAuthPath?;
    private successAuthPath?;
    private signinAuthPath?;
    constructor(widgetConfig: WidgetConfig);
    signup(formData: any): Promise<Response>;
    resetPassword(formData: any): Promise<Response>;
    resendConfirmation(formData: any): Promise<Response>;
    getApiBase(): string;
    getOrganizationDetails(): Promise<OrganizationDetailsResponse>;
    checkAuthStatus(): Promise<AuthResponse>;
}
